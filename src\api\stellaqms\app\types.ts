// 定义用户登录参数接口
export interface UserLoginVO {
  username: string
  password: string
  userType: 'BRAND' | 'NORMAL'
}

// 定义返回的令牌接口
export interface TokenVO {
  token: string
}

// 定义JWT解析结果
export interface JwtPayload {
  userId?: string
  username?: string
  nickname?: string
  factoryId?: string
  roleCodes?: string[]
  permissions?: string[]
  factoryIds?: string[]
  brandIds?: string[]
  deptIds?: string[]
  invFlag?: string
  exp?: number
  iat?: number
}

// 定义角色接口
export interface RoleVO {
  id: number
  name: string
  code: string
  description?: string
  status: number
  createTime?: Date
  permissionIds?: number[]
}

// 定义角色分页查询参数
export interface RolePageReqVO {
  name?: string
  code?: string
  status?: number
  page: number
  size: number
}

// 分页响应数据
export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// 定义用户接口
export interface UserVO {
  id: number
  username: string
  nickname: string
  userType?: 'BRAND' | 'NORMAL'
  avatar?: string
  sex?: number
  phone?: string
  mobile?: string
  email?: string
  groupId?: string
  status: number
  deptId?: number
  createTime?: Date
  roleIds?: number[]
  password?: string
}

// 定义用户分页查询参数
export interface UserPageReqVO {
  keyword?: string
  username?: string
  mobile?: string
  userType?: string
  status?: number
  deptId?: number
  page: number
  size: number
}

// 定义权限接口
export interface PermissionVO {
  id: number
  name: string
  parentId: number
  type: number
  url?: string
  code?: string
  path?: string
  sort?: number
  status?: number
  createTime?: Date
  children?: PermissionVO[]
}

// 定义权限查询参数
export interface PermissionQueryVO {
  name?: string
  code?: string
  status?: number
}

// 定义权限创建/更新参数
export interface PermissionFormVO {
  id?: number | null
  parentId: number
  name: string
  code: string
  type: number
  url?: string
  sort?: number
  status: number
}

// 定义数据范围接口
export interface DataScopeVO {
  roleId: number | null
  scopeType: number  // 1: 全部数据, 2: 按工厂, 3: 按工厂和品牌
  factoryIds: string[]
  brandIds: string[]
}

// 定义部门接口
export interface DeptVO {
  id: number
  name: string
  parentId: number
  status: number
  children?: DeptVO[]
}

// 定义密码重置参数
export interface ResetPasswordVO {
  newPassword: string
}

// 定义部门角色关联DTO
export interface DepartmentRoleDTO {
  id?: number
  factoryId: string
  departmentId: number
  roleType: number
  userId: number
}

// 定义部门角色关联VO
export interface DepartmentRoleVO {
  id?: number
  factoryId: string
  departmentId: number
  departmentName?: string
  roleType: number
  roleTypeName?: string
  userId: number
  username?: string
  nickname?: string
}

// 定义问题角色人员VO
export interface IssueRolePersonsVO {
  responsiblePerson?: UserVO  // 责任人
  qcSupervisor?: UserVO       // QC主管
  factorySupervisor?: UserVO  // 厂务主管
  factoryManager?: UserVO     // 厂主管
}

// 定义批量保存部门角色请求
export interface BatchSaveDepartmentRoleRequest {
  factoryId: string
  departmentId: number
  roleMap: Record<number, number> // roleType -> userId
} 