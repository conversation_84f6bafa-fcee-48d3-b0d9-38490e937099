<script setup lang="ts">
import { ref, computed, onUnmounted, nextTick, watch } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { useResizeObserver } from '@vueuse/core';

// Register ECharts components
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  BarChart,
  CanvasRenderer,
]);

// 定义指标类型
type MetricType = 'shoeDefectRate' | 'unboxingRate' | 'qualityComplaints' | 'complaintAmount';

// 定义工厂数据接口
interface FactoryMetricData {
  factNo: string;
  value: number;
}

// 定义组件属性
interface Props {
  visible: boolean;
  metricType: MetricType;
  title: string;
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  'update:visible': [value: boolean];
  'openProductionLine': [factNo: string, factName: string];
}>();

// 响应式数据
const chartData = ref<FactoryMetricData[]>([]);
const chartRef = ref<HTMLElement | null>(null);
const loading = ref(false);
let chartInstance: echarts.ECharts | null = null;

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 获取指标单位
const getUnit = (type: MetricType): string => {
  switch (type) {
    case 'shoeDefectRate':
    case 'unboxingRate':
      return '%';
    case 'complaintAmount':
      return '元';
    default:
      return '';
  }
};

// 格式化数值显示
const formatValue = (value: number, type: MetricType): string => {
  switch (type) {
    case 'shoeDefectRate':
    case 'unboxingRate':
      return `${value}%`;
    case 'complaintAmount':
      return `¥${value.toLocaleString()}`;
    default:
      return value.toString();
  }
};

// 获取图表颜色
const getChartColor = (type: MetricType): string => {
  switch (type) {
    case 'shoeDefectRate':
      return '#F39C12';
    case 'unboxingRate':
      return '#8E44AD';
    case 'qualityComplaints':
      return '#3498DB';
    case 'complaintAmount':
      return '#2ECC71';
    default:
      return '#3498DB';
  }
};

// 图表配置
const chartOption = computed(() => ({
  backgroundColor: 'transparent',
  title: {
    text: `${props.title} - 工厂对比`,
    left: 'center',
    textStyle: {
      color: '#303133',
      fontSize: 16,
      fontWeight: 'normal'
    },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: { 
      type: 'shadow', 
      shadowStyle: { color: 'rgba(150, 150, 150, 0.1)' } 
    },
    backgroundColor: '#FFFFFF',
    borderColor: '#DCDFE6',
    borderWidth: 1,
    textStyle: { 
      color: '#303133',
      fontSize: 12
    },
    padding: [8, 12],
    formatter: (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      if (param && typeof param.name !== 'undefined' && typeof param.value !== 'undefined') {
        return `${param.name}: ${formatValue(param.value, props.metricType)}`;
      }
      return '';
    }
  },
  grid: { 
    top: '80', 
    right: '30', 
    bottom: '60', 
    left: '60' 
  },
  xAxis: {
    type: 'category',
    data: chartData.value.map(item => item.factNo),
    axisLine: { lineStyle: { color: '#DCDFE6' } },
    splitLine: { show: false },
    axisTick: { show: false },
    axisLabel: { 
      color: '#909399',
      fontSize: 12,
      rotate: 0
    },
  },
  yAxis: {
    type: 'value',
    axisLine: { lineStyle: { color: '#DCDFE6' } },
    splitLine: { 
      lineStyle: { 
        color: '#EBEEF5', 
        type: 'dashed', 
        opacity: 0.8 
      } 
    },
    axisTick: { show: false },
    axisLabel: { 
      color: '#909399',
      fontSize: 12,
      formatter: `{value}${getUnit(props.metricType)}`
    },
  },
  series: [{
    name: props.title,
    type: 'bar',
    data: chartData.value.map(item => item.value),
    itemStyle: { 
      color: getChartColor(props.metricType),
      borderRadius: [4, 4, 0, 0] 
    },
    barWidth: '50%',
    emphasis: { 
      itemStyle: { 
        color: getChartColor(props.metricType),
        opacity: 0.8
      } 
    }
  }],
}));

// 获取工厂数据
const fetchFactoryData = async () => {
  try {
    loading.value = true;
    // TODO: 调用实际API
    // const res = await getFactoryMetricData(props.metricType);
    // chartData.value = res;
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 800));
    
    const mockData: Record<MetricType, FactoryMetricData[]> = {
      shoeDefectRate: [
        { factNo: 'AL', value: 2.8 },
        { factNo: 'TP', value: 1.9 },
        { factNo: 'GN', value: 2.1 },
        { factNo: 'SS', value: 1.5 },
        { factNo: 'SF', value: 1.2 },
        { factNo: 'HN', value: 2.3 },
        { factNo: 'BJ', value: 1.8 },
        { factNo: 'SZ', value: 2.0 }
      ],
      unboxingRate: [
        { factNo: 'AL', value: 18.5 },
        { factNo: 'TP', value: 12.3 },
        { factNo: 'GN', value: 15.7 },
        { factNo: 'SS', value: 9.8 },
        { factNo: 'SF', value: 11.2 },
        { factNo: 'HN', value: 16.4 },
        { factNo: 'BJ', value: 13.9 },
        { factNo: 'SZ', value: 14.6 }
      ],
      qualityComplaints: [
        { factNo: 'AL', value: 8 },
        { factNo: 'TP', value: 5 },
        { factNo: 'GN', value: 6 },
        { factNo: 'SS', value: 3 },
        { factNo: 'SF', value: 4 },
        { factNo: 'HN', value: 7 },
        { factNo: 'BJ', value: 5 },
        { factNo: 'SZ', value: 6 }
      ],
      complaintAmount: [
        { factNo: 'AL', value: 15600 },
        { factNo: 'TP', value: 8900 },
        { factNo: 'GN', value: 12300 },
        { factNo: 'SS', value: 5600 },
        { factNo: 'SF', value: 7800 },
        { factNo: 'HN', value: 13400 },
        { factNo: 'BJ', value: 9200 },
        { factNo: 'SZ', value: 10500 }
      ]
    };
    
    chartData.value = mockData[props.metricType] || [];
  } catch (error) {
    console.error('获取工厂数据失败:', error);
    chartData.value = [];
  } finally {
    loading.value = false;
  }
};

// 初始化图表
const initChart = () => {
  if (chartRef.value && !chartInstance) {
    chartInstance = echarts.init(chartRef.value);
    chartInstance.setOption(chartOption.value);
    
    // 添加点击事件（仅对理鞋不良率有效）
    if (props.metricType === 'shoeDefectRate') {
      chartInstance.on('click', (params: any) => {
        if (params.componentType === 'series') {
          const factNo = params.name;
          emit('openProductionLine', factNo, `${factNo}工厂`);
        }
      });
    }
  }
};

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(chartOption.value, { notMerge: true });
  } else {
    nextTick(initChart);
  }
};

// 调整图表大小
const resizeChart = () => {
  chartInstance?.resize();
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    fetchFactoryData();
    nextTick(() => {
      initChart();
      if (chartRef.value) {
        useResizeObserver(chartRef.value, resizeChart);
      }
    });
  }
});

// 监听图表数据变化
watch(chartData, () => {
  updateChart();
}, { deep: true });

// 组件卸载时清理
onUnmounted(() => {
  chartInstance?.dispose();
  chartInstance = null;
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${title} - 工厂对比分析`"
    width="900px"
    class="factory-comparison-dialog"
    destroy-on-close
    @close="chartInstance?.dispose(); chartInstance = null;"
  >
    <div class="dialog-content" v-loading="loading" element-loading-text="正在加载数据...">
      <div class="chart-container">
        <div ref="chartRef" class="chart-wrapper"></div>
      </div>
      
      <div class="chart-tips" v-if="metricType === 'shoeDefectRate'">
        <el-alert
          title="提示"
          type="info"
          :closable="false"
          show-icon
        >
          <template #default>
            点击柱状图可查看该工厂各线别的详细数据
          </template>
        </el-alert>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.factory-comparison-dialog {
  .el-dialog__header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.dialog-content {
  min-height: 400px;
}

.chart-container {
  width: 100%;
  height: 400px;
  margin-bottom: 20px;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
}

.chart-tips {
  margin-top: 15px;
  
  :deep(.el-alert) {
    border-radius: 4px;
    
    .el-alert__content {
      font-size: 13px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
