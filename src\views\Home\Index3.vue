<script setup lang="ts">
import { ref, reactive, defineAsyncComponent, onMounted } from 'vue';
import { Search, Setting } from '@element-plus/icons-vue'
import draggable from 'vuedraggable';
// 导入工厂列表API和高不良率工单API
import { getFactoryList, getHighDefectWorkOrders } from '@/api/stellaqms/dashboard';
// 导入生产状态弹窗及其API
import ProductionStatusDialog from '@/views/stellaqms/onlineQualityInspection/components/ProductionStatusDialog.vue';
import { getOrderList } from '@/api/stellaqms/onlineQualityInspection/orderManagement';
// 导入AI摘要按钮组件
import AISummaryButton from '@/components/AISummaryButton/index.vue';
// 导入AI分析API
import { ElMessage } from 'element-plus'
import { AiAnalysisApi } from '@/api/ai/analysis/index';
// import { useI18n } from '@/hooks/web/useI18n'; // 如果弹窗或其内部逻辑需要


defineOptions({ name: 'Home' })

const _searchIcon = Search;
const _settingIcon = Setting;

const KpiSummaryCard = defineAsyncComponent(() => import("./components/KpiSummaryCard.vue"));
const ListCard = defineAsyncComponent(() => import("./components/ListCard.vue"));

const Top5IssuesDoughnutChart = defineAsyncComponent(() => import("./components/Top5IssuesDoughnutChart.vue"));
const FactoryComparisonBarChart = defineAsyncComponent(() => import("./components/FactoryComparisonBarChart.vue"));
const QAIssueRankingComponent = defineAsyncComponent(() => import("./components/QAIssueRankingComponent.vue"));
const BrandDefectRateChart = defineAsyncComponent(() => import("./components/BrandDefectRateChart.vue"));
const CustomerComplaintsComponent = defineAsyncComponent(() => import("./components/CustomerComplaintsComponent.vue"));
const BrandUnboxingRateTable = defineAsyncComponent(() => import("./components/BrandUnboxingRateTable.vue"));
const QipAuditComponent = defineAsyncComponent(() => import("./components/QipAuditComponent.vue"));
const QualityMetricsCard = defineAsyncComponent(() => import("./components/QualityMetricsCard.vue"));
const FactoryComparisonDialog = defineAsyncComponent(() => import("./components/FactoryComparisonDialog.vue"));
const ProductionLineDialog = defineAsyncComponent(() => import("./components/ProductionLineDialog.vue"));

interface OvertimeAlert {
  id: number;
  task: string;
  inspector: string;
  deadline: string;
  status: 'Urgent' | 'Overdue' | 'Pending';
}

interface IssueStatus {
  status: string;
  count: number;
  total: number;
}

interface MyTask {
    id: number;
    title: string;
    type: '审批' | '检验' | '整改';
    status: '待处理' | '进行中' | '已完成';
    dueDate?: string;
}

// const { t } = useI18n(); // 暂时注释，如果需要再启用

const selectedFactory = ref('');

const factoryOptions = ref<string[]>([]);

const modelSearch = ref('');

const issueStatusLast3Days = ref<IssueStatus[]>([
  { status: 'Pending', count: 5, total: 20 },
  { status: 'In Progress', count: 8, total: 20 },
  { status: 'Resolved', count: 7, total: 20 },
]);

const myTasks = ref<MyTask[]>([
    { id: 1, title: '审核零件 A 的检验报告', type: '审批', status: '待处理', dueDate: '2024-03-18' },
    { id: 2, title: '执行成品 B 的出厂检验', type: '检验', status: '进行中' },
    { id: 3, title: '处理批次 C 的喷涂缺陷', type: '整改', status: '待处理', dueDate: '2024-03-19' },
    { id: 4, title: '复核供应商 D 的资质文件', type: '审批', status: '待处理' },
    { id: 5, title: '确认设备 E 的校准记录', type: '检验', status: '已完成' },
]);

interface QuickLink {
    id: number;
    text: string;
    icon: string;
}

const quickLinks = ref<QuickLink[]>([
    { id: 1, text: '发起检验', icon: 'ph:clipboard-text-bold' },
    { id: 2, text: '登记不良', icon: 'ph:warning-octagon-bold' },
    { id: 3, text: '查询标准', icon: 'ph:book-open-text-bold' },
    { id: 4, text: '质量报表', icon: 'ph:chart-bar-horizontal-bold' },
    { id: 5, text: '历史记录', icon: 'ph:clock-counter-clockwise-bold' },
    { id: 6, text: '系统设置', icon: 'ph:gear-six-bold' },
]);

interface HighDefectWorkOrder {
  id: string;
  rate: number;
  model?: string;
  factory?: string;
  productionDate?: string;
  defectCount?: number;
  inspectionCount?: number;
}

const highDefectWorkOrders = ref<HighDefectWorkOrder[]>([]);
const highDefectDialogVisible = ref(false);

// 获取不良率偏高工单数据
const fetchHighDefectWorkOrders = async () => {
  try {
    const res = await getHighDefectWorkOrders();
    highDefectWorkOrders.value = res || [];
  } catch (error) {
    console.error('获取不良率偏高工单数据失败:', error);
    highDefectWorkOrders.value = [];
  }
};

const wabiSabiColors = {
  accent: 'var(--color-primary)',
  chartGrayDark: 'var(--color-text-regular)',
  chartGrayMedium: 'var(--color-text-secondary)',
  chartGrayLight: 'var(--color-border-light)',
  dangerSubtle: 'var(--color-urgent)',
  warningSubtle: 'var(--color-overdue)',
  background: 'var(--color-white)',
  backgroundSubtle: 'var(--color-background-base)',
  textPrimary: 'var(--color-text-primary)',
  textSecondary: 'var(--color-text-secondary)',
  border: 'var(--color-border-base)',
};

const getAlertStatusClass = (status: OvertimeAlert['status']): string => {
    switch (status) {
        case 'Urgent': return 'alert-status-urgent';
        case 'Overdue': return 'alert-status-overdue';
        case 'Pending': return 'alert-status-pending';
        default: return 'alert-status-default';
    }
}

const getTaskStatusClass = (status: MyTask['status']): string => {
    switch (status) {
        case '待处理': return 'task-status-pending';
        case '进行中': return 'task-status-progress';
        case '已完成': return 'task-status-completed';
        default: return 'task-status-default';
    }
};

interface CardDefinition {
  id: string;
  title: string;
  component?: ReturnType<typeof defineAsyncComponent> | null;
  visible: boolean;
  gridSpan?: number;
  initialOrder: number;
  content?: 'kpi' | 'list' | 'status' | 'placeholder' | 'chart' | 'customTop5IssuesChart' | 'customFactoryComparisonChart' | 'customQAIssueRanking' | 'customBrandDefectRateChart' | 'customCustomerComplaints' | 'customBrandUnboxingTable' | 'customQipAudit' | 'customQualityMetrics';
  listType?: 'qaAlerts' | 'myTasks';
}

const allCards = reactive<CardDefinition[]>([
  { id: 'kpiSummary', title: 'KPI 汇总', component: null, visible: true, gridSpan: 1, initialOrder: 1, content: 'kpi' },
  { id: 'qualityMetrics', title: '质量关键指标', component: QualityMetricsCard, visible: true, gridSpan: 2, initialOrder: 2, content: 'customQualityMetrics' },
  { id: 'top5Issues', title: '前5大问题点', component: Top5IssuesDoughnutChart, visible: true, gridSpan: 1, initialOrder: 3, content: 'customTop5IssuesChart' },
  { id: 'qaIssueRanking', title: 'QA问题排行', component: QAIssueRankingComponent, visible: true, gridSpan: 1, initialOrder: 4, content: 'customQAIssueRanking' },
  { id: 'brandDefectRate', title: '品牌不良率', component: BrandDefectRateChart, visible: true, gridSpan: 1, initialOrder: 5, content: 'customBrandDefectRateChart' },
  { id: 'factoryDefectComparison', title: '最终验货不良率', component: FactoryComparisonBarChart, visible: true, gridSpan: 1, initialOrder: 6, content: 'customFactoryComparisonChart' },
  { id: 'brandUnboxingRate', title: '品牌翻箱率', component: BrandUnboxingRateTable, visible: true, gridSpan: 1, initialOrder: 7, content: 'customBrandUnboxingTable' },
  { id: 'qipAudit', title: 'QIP稽核', component: QipAuditComponent, visible: true, gridSpan: 1, initialOrder: 8, content: 'customQipAudit' },
  { id: 'customerComplaints', title: '最新客诉问题', component: CustomerComplaintsComponent, visible: true, gridSpan: 1, initialOrder: 9, content: 'customCustomerComplaints' },
]);

// 质量指标弹窗相关状态
const factoryComparisonDialogVisible = ref(false);
const productionLineDialogVisible = ref(false);
const currentMetricType = ref<'shoeDefectRate' | 'unboxingRate' | 'qualityComplaints' | 'complaintAmount'>('shoeDefectRate');
const currentMetricTitle = ref('');
const currentFactoryNo = ref('');
const currentFactoryName = ref('');

// 生产状态弹窗相关状态
const statusDialogVisible = ref(false);
const statusDialogData = ref({
  brandName: '',
  factNo: '',
  custPo: '',
  modelNo: '',
  orderNo: '',
  buyMonth: '',
  poQuantity: 0,
  style: '',
  color: '',
  defectRate: '0%',
  materialPreparation: false,
  cuttingStarted: false,
  productionCompleted: false,
  inspectionCompleted: false,
  orderShipped: false
});
const startDateForDialog = ref('');
const endDateForDialog = ref('');
const currentLoadingOrder = ref(false); // 用于显示加载状态，可选

// 设置弹窗的默认起止日期 (例如最近30天)
const setupDialogDates = () => {
  const today = new Date();
  const thirtyDaysAgo = new Date(today);
  thirtyDaysAgo.setDate(today.getDate() - 30);
  startDateForDialog.value = thirtyDaysAgo.toISOString().split('T')[0];
  endDateForDialog.value = today.toISOString().split('T')[0];
};

// 打开生产状态弹窗的逻辑
async function openProductionStatusDialog(orderOrOrderNo: HighDefectWorkOrder | string) {
  currentLoadingOrder.value = true;
  highDefectDialogVisible.value = false;
  let orderId: string;
  let initialDefectRateStr: string;

  if (typeof orderOrOrderNo === 'string') {
    orderId = orderOrOrderNo;
    initialDefectRateStr = '0%'; // 来自搜索框，初始不良率未知，设为0%或特定提示
    if (!orderId) {
      ElMessage.warning('请输入工单号进行搜索');
      currentLoadingOrder.value = false;
      return;
    }
  } else {
    orderId = orderOrOrderNo.id;
    initialDefectRateStr = `${orderOrOrderNo.rate}%`;
  }

  try {
    const params = { 
      workOrderNo: orderId, 
      pageNo: 1, 
      pageSize: 1, 
      factNo: selectedFactory.value || undefined
    };
    const res = await getOrderList(params);

    if (res && res.list && res.list.length > 0) {
      const row = res.list[0];
      statusDialogData.value = {
        brandName: row.brandName || 'N/A',
        factNo: row.factNo || selectedFactory.value,
        custPo: row.custPo || 'N/A',
        modelNo: row.modelNo || 'N/A',
        orderNo: row.orderNo || orderId, // 确保使用传入或查询到的orderId
        buyMonth: row.selectDate ? new Date(row.selectDate).toISOString().substring(0, 7).replace('-', '/') : new Date().toISOString().substring(0, 7).replace('-', '/'),
        poQuantity: row.ordQty || 0,
        style: row.modelNo || '',
        color: row.colorName || '',
        defectRate: row.timeNum > 0 && typeof row.blNum === 'number' ? (row.blNum / row.timeNum * 100).toFixed(2) + '%' : initialDefectRateStr,
        materialPreparation: row.materialPreparation !== undefined ? row.materialPreparation : true, 
        cuttingStarted: row.cuttingStarted !== undefined ? row.cuttingStarted : true, 
        productionCompleted: row.productionCompleted !== undefined ? row.productionCompleted : (row.timeNum > 0),
        inspectionCompleted: row.inspectionCompleted !== undefined ? row.inspectionCompleted : (row.timeNum > 0),
        orderShipped: row.orderShipped !== undefined ? row.orderShipped : (row.timeNum > 0 && typeof row.blNum === 'number' && (row.blNum / row.timeNum < 0.05)),
      };
    } else {
      ElMessage.warning('未能获取工单详细信息，将使用基本信息展示');
      statusDialogData.value = {
        brandName: 'N/A',
        factNo: selectedFactory.value || 'N/A',
        custPo: 'N/A',
        modelNo: 'N/A',
        orderNo: orderId, // 确保使用传入的orderId
        buyMonth: new Date().toISOString().substring(0, 7).replace('-', '/'),
        poQuantity: 0, // 修改为数字类型
        style: 'N/A', // 伪数据
        color: 'N/A', // 伪数据
        defectRate: initialDefectRateStr, // 使用初始不良率字符串
        materialPreparation: true, // 伪数据
        cuttingStarted: true, // 伪数据
        productionCompleted: false, 
        inspectionCompleted: false,
        orderShipped: false,
      };
    }
    // 确保弹窗数据准备好后再显示弹窗
    setTimeout(() => {
      statusDialogVisible.value = true;
    }, 0);
  } catch (error) {
    console.error('获取工单详情失败:', error);
    ElMessage.warning('获取工单详情失败，将使用基本信息展示');
    statusDialogData.value = {
      brandName: 'N/A',
      factNo: selectedFactory.value || 'N/A',
      custPo: 'N/A',
      modelNo: 'N/A',
      orderNo: orderId, // 确保使用传入的orderId
      buyMonth: new Date().toISOString().substring(0, 7).replace('-', '/'),
      poQuantity: 0, // 修改为数字类型
      style: 'N/A',
      color: 'N/A',
      defectRate: initialDefectRateStr, // 使用初始不良率字符串
      materialPreparation: true,
      cuttingStarted: true,
      productionCompleted: false,
      inspectionCompleted: false,
      orderShipped: false,
    };
    // 确保弹窗数据准备好后再显示弹窗
    setTimeout(() => {
      statusDialogVisible.value = true;
    }, 0);
  } finally {
    currentLoadingOrder.value = false;
  }
}

const draggableKey = ref(0);

const loadLayoutConfig = () => {
  try {
    // 尝试从localStorage获取布局配置
    const savedConfigStr = localStorage.getItem('dashboardLayoutConfig_v3');
    if (!savedConfigStr) {
      console.log('没有找到保存的布局配置，使用默认顺序');
      return;
    }
    
    // 解析配置
    const savedConfig = JSON.parse(savedConfigStr);
    if (!Array.isArray(savedConfig) || savedConfig.length === 0) {
      console.log('布局配置无效，使用默认顺序');
      return;
    }
    
    console.log('加载已保存的布局配置:', savedConfig);
    
    // 创建ID到保存卡片的映射
    const savedCardMap = new Map();
    savedConfig.forEach(card => {
      savedCardMap.set(card.id, card);
    });
    
    // 应用可见性设置
    allCards.forEach(card => {
      const savedCard = savedCardMap.get(card.id);
      if (savedCard) {
        card.visible = savedCard.visible;
      }
    });
    
    // 创建一个新的数组，按照保存的顺序排列卡片
    const newOrderedCards: CardDefinition[] = [];
    for (const savedCard of savedConfig) {
      const card = allCards.find(c => c.id === savedCard.id);
      if (card) {
        newOrderedCards.push(card);
      }
    }
    
    // 添加任何新增的卡片（不在保存配置中的）
    allCards.forEach(card => {
      if (!newOrderedCards.includes(card)) {
        newOrderedCards.push(card);
      }
    });
    
    // 清空allCards数组
    allCards.splice(0, allCards.length);
    
    // 将排序后的卡片添加到allCards
    newOrderedCards.forEach(card => {
      allCards.push(card);
    });
    
    // 确保configCards与allCards保持同步
    configCards.value = JSON.parse(JSON.stringify(allCards));
    
    console.log('布局配置加载完成，卡片顺序已更新');
  } catch (error) {
    console.error('加载布局配置出错:', error);
  }
};

const saveLayoutConfig = () => {
  try {
    // 创建一个简化的配置对象数组，保存ID、可见性和顺序
    const configToSave = configCards.value.map((card, index) => ({
      id: card.id,
      visible: card.visible,
      order: index
    }));
    
    // 保存到localStorage
    localStorage.setItem('dashboardLayoutConfig_v3', JSON.stringify(configToSave));
    console.log('布局配置已保存:', configToSave);
    
    // 增加draggableKey触发视图更新
    draggableKey.value++;
  } catch (error) {
    console.error('保存布局配置出错:', error);
  }
};

const onDragEnd = () => {
    console.log('拖拽结束事件触发。');
    // 此时，vuedraggable 已经修改了 `allCards` 数组的顺序以匹配用户的拖拽操作。
    // `allCards` 是响应式的，并且是 `vuedraggable` 的 `v-model`。
    // `event.oldIndex` 和 `event.newIndex` 描述了元素在 `allCards` 中的移动。

    console.log('vuedraggable 更新后的 allCards (实际顺序):', JSON.parse(JSON.stringify(allCards)));

    // 步骤 1: 更新 `configCards` 以匹配 `allCards` 的新顺序，同时保留 `visible` 状态。
    // `configCards` 用于配置弹窗和保存布局。
    
    // 创建一个映射，用于从旧的 `configCards` 中查找 `visible` 状态。
    const oldConfigVisibleStates = new Map();
    if (Array.isArray(configCards.value)) {
        configCards.value.forEach(c => {
            oldConfigVisibleStates.set(c.id, c.visible);
        });
    }

    // 基于 `allCards` 的新顺序构建新的 `configCards`。
    configCards.value = allCards.map(cardFromAllCards => {
        // cardFromAllCards 包含了拖拽后的正确顺序和卡片数据。
        return {
            ...cardFromAllCards, // 复制所有属性 (id, title, component, gridSpan, content, etc.)
            visible: oldConfigVisibleStates.get(cardFromAllCards.id) ?? cardFromAllCards.visible, // 保留之前的可见性设置，如果找不到则使用 allCards 中的可见性
        };
    });

    console.log('同步到 configCards 后的 configCards.value:', JSON.parse(JSON.stringify(configCards.value)));

    // 步骤 2: 调用 `saveLayoutConfig` 保存新的布局。
    // `saveLayoutConfig` 内部会使用 `configCards.value`。
    saveLayoutConfig();
    
    // 步骤 3: 增加 `draggableKey` 以尝试强制 `vuedraggable` 组件重新渲染。
    // 这是为了解决视图可能不会立即更新的问题，特别是在某些环境或构建下。
    draggableKey.value++;
    console.log('draggableKey 已更新为:', draggableKey.value, '。视图应刷新。');
};

const showConfigDialog = ref(false);
const configCards = ref<CardDefinition[]>([]);

const openConfigDialog = () => {
  showConfigDialog.value = true;
};

const applyConfigChanges = () => {
  // 更新可见性并保持configCards的排序顺序
  // 创建一个新的数组，按照configCards的顺序排列
  const newOrderedCards: CardDefinition[] = [];
  
  // 首先按照configCards的顺序添加卡片
  configCards.value.forEach(configCard => {
    const originalCard = allCards.find(c => c.id === configCard.id);
    if (originalCard) {
      // 更新可见性
      originalCard.visible = configCard.visible;
      newOrderedCards.push(originalCard);
    }
  });
  
  // 添加可能在allCards中但不在configCards中的卡片（理论上不应该发生）
  allCards.forEach(card => {
    if (!newOrderedCards.includes(card)) {
      newOrderedCards.push(card);
    }
  });
  
  // 清空allCards数组
  allCards.splice(0, allCards.length);
  
  // 将新排序的卡片添加到allCards
  newOrderedCards.forEach(card => {
    allCards.push(card);
  });
  
  // 保存配置并关闭对话框
  saveLayoutConfig();
  showConfigDialog.value = false;
  console.log('已应用新的卡片顺序和可见性设置');
};

const cancelConfigChanges = () => {
  showConfigDialog.value = false;
};

const fetchMoreHighDefectData = () => {
  // 显示高不良率工单明细弹窗
  if (highDefectWorkOrders.value.length > 0) {
    highDefectDialogVisible.value = true;
  } else {
    ElMessage.info('暂无不良率偏高工单数据');
  }
};

const clearLayoutConfig = () => {
  localStorage.removeItem('dashboardLayoutConfig_v3');
  window.location.reload();
};

// 获取工厂列表数据
const fetchFactoryList = async () => {
  try {
    const res = await getFactoryList();
    factoryOptions.value = res;
    // 不再设置默认选中工厂
  } catch (error) {
    console.error('获取工厂列表失败:', error);
  }
};

// 为AI摘要组件准备数据获取函数
const getDashboardDataForAI = async () => {
  try {
    // 直接调用AI分析API，传入当前页面的基本信息
    const requestData = {
      factoryId: selectedFactory.value ? parseInt(selectedFactory.value) : undefined,
      dateRange: [startDateForDialog.value, endDateForDialog.value].filter(Boolean),
      // 可以添加其他需要的参数
    };

    // 调用AI分析API
    const response = await AiAnalysisApi.getDashboardSummary(requestData);
    
    // 直接返回AI分析结果
    return response.data || response.aiAnalysisResult || '暂无分析结果';
  } catch (error) {
    console.error('获取AI分析结果失败:', error);
    throw error;
  }
};

// 质量指标事件处理 - 现在直接在组件内部处理，不需要弹窗
const handleOpenProductionLine = (factNo: string, factName: string) => {
  currentFactoryNo.value = factNo;
  currentFactoryName.value = factName;
  productionLineDialogVisible.value = true;
};

// AI摘要组件事件处理
const handleSummaryGenerated = (summary: string) => {
  console.log('AI摘要生成成功:', summary);
  // 可以在这里添加额外的处理逻辑，比如保存到本地存储等
};

const handleSummaryError = (error: string) => {
  console.error('AI摘要生成失败:', error);
  // 可以在这里添加错误处理逻辑
};

onMounted(() => {
  loadLayoutConfig();
  // 确保初始时configCards与allCards同步
  configCards.value = JSON.parse(JSON.stringify(allCards));
  // 获取工厂列表
  fetchFactoryList();
  // 设置弹窗日期
  setupDialogDates();
  // 获取不良率偏高工单数据
  fetchHighDefectWorkOrders();
});
</script>

<template>
  <div class="dashboard-layout">
    <ElButton :icon="_settingIcon" circle @click="openConfigDialog" title="配置卡片" class="config-button" />
    <div class="dashboard-topbar">
      <div class="search-filter-wrapper grid-card">
        <div class="card-inner-content" v-loading="currentLoadingOrder" element-loading-text="正在加载数据...">
          <h3 class="card-title-inner">型體/工單/PO查詢</h3>
          <div class="search-controls">
            <ElSelect 
              v-model="selectedFactory" 
              placeholder="选择工厂" 
              class="header-select" 
              style="width: 200px;" 
              filterable
              clearable
            >
              <ElOption
                v-for="item in factoryOptions"
                :key="item"
                :label="item"
                :value="item"
              />
            </ElSelect>
            <ElInput
               v-model="modelSearch"
               placeholder="搜索工单、型体..."
               clearable
               class="header-search-input"
               :prefix-icon="_searchIcon"
               style="width: 300px;"
               :disabled="currentLoadingOrder"
            />
            <ElButton 
              title="搜索" 
              class="search-trigger-button" 
              type="primary" 
              @click="openProductionStatusDialog(modelSearch)" 
              :loading="currentLoadingOrder"
            >
              搜索
            </ElButton>
          </div>
          <div class="high-defect-workorders">
            <span>不良率偏高的工單: </span>
            <template v-if="highDefectWorkOrders.length > 0">
              <span v-for="(order, index) in highDefectWorkOrders.slice(0, 2)" :key="order.id">
                <a href="#" @click.prevent="openProductionStatusDialog(order)" class="workorder-item-link" :class="{ 'disabled': currentLoadingOrder }">
                  {{ order.id }}({{ order.rate }}%)
                </a>
                <span v-if="index < highDefectWorkOrders.slice(0, 2).length - 1" class="separator">、</span>
              </span>
              <a href="#" @click.prevent="fetchMoreHighDefectData" class="more-link" v-if="highDefectWorkOrders.length > 2" :class="{ 'disabled': currentLoadingOrder }">......查詢更多數據</a>
            </template>
            <template v-else>
              <span class="no-data">暂无数据</span>
            </template>
          </div>
        </div>
      </div>
      <div class="common-functions-section grid-card">
        <div class="card-inner-content">
          <h3 class="card-title-inner">快捷入口</h3>
          <div class="function-items">
            <div v-for="link in quickLinks" :key="link.id" class="function-item">
              <span class="placeholder-icon">
                <Icon :icon="link.icon" />
              </span>
              <span>{{ link.text }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <main class="dashboard-grid">
       <draggable
         v-model="allCards"
         item-key="id"
         tag="main"
         class="dashboard-grid"
         handle=".drag-handle"
         :animation="200"
         ghost-class="ghost-card"
         chosen-class="chosen-card"
         @end="onDragEnd"
         :key="draggableKey"
       >
         <template #item="{ element: card }">
           <div
             v-show="card.visible"
             :key="card.id"
             :class="['grid-card', `card-${card.id}`]"
             :style="{ gridColumn: card.gridSpan ? `span ${card.gridSpan}` : 'span 1' }"
           >
             <div class="drag-handle" title="拖拽排序" style="position: absolute; top: 0; left: 0; width: 100%; height: 10px; cursor: move; z-index: 1;"></div>

             <div class="card-inner-content">
                <template v-if="card.content === 'kpi'">
                  <KpiSummaryCard/>
                </template>

                <template v-else-if="card.content === 'chart' && card.component">
                  <h3 class="card-title-inner">{{ card.title }}</h3>
                  <component
                      :is="card.component"
                      :colors="wabiSabiColors"
                      class="chart-instance"
                  />
                </template>

                <template v-else-if="card.content === 'list' && card.listType">
                   <ListCard
                       :title="card.title"
                       :list-type="card.listType"
                       :items="myTasks"
                       :get-alert-status-class="getAlertStatusClass"
                       :get-task-status-class="getTaskStatusClass"
                    />
                </template>

                <template v-else-if="card.content === 'status'">
                    <h3 class="card-title-inner">{{ card.title }}</h3>
                    <div class="issue-status-list card-content-static">
                         <div v-for="status_item_detail in issueStatusLast3Days" :key="status_item_detail.status" class="issue-status-item">
                             <div class="status-info">
                                 <span>{{ status_item_detail.status }}</span>
                                 <span>{{ status_item_detail.count }} / {{ status_item_detail.total }}</span>
                             </div>
                             <ElProgress
                                 :percentage="(status_item_detail.count / status_item_detail.total) * 100"
                                 :stroke-width="6"
                                 :color="status_item_detail.status === 'Pending' ? 'var(--color-kpi-orange)' : status_item_detail.status === 'In Progress' ? 'var(--color-primary)' : 'var(--color-kpi-green)'"
                                 :show-text="false"
                                 class="status-progress"
                                 />
                         </div>
                    </div>
                </template>

                <template v-else-if="card.content === 'customQualityMetrics'">
                  <component
                      :is="card.component"
                      @openProductionLine="handleOpenProductionLine"
                      class="chart-instance"
                  />
                </template>

                <template v-else-if="card.component && card.content && card.content.startsWith('custom')">
                  <h3 class="card-title-inner">{{ card.title }}</h3>
                  <component
                      :is="card.component"
                      :colors="wabiSabiColors"
                      :title="card.title" 
                      class="chart-instance"
                  />
                </template>

                <template v-else-if="card.content === 'placeholder'">
                     <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--color-text-secondary); background-color: var(--color-background-base);">
                         空卡片
                     </div>
                </template>
             </div>
           </div>
         </template>
       </draggable>

       <ElDialog v-model="showConfigDialog" title="配置卡片显隐与顺序" width="500px">
          <draggable
              v-model="configCards"
              item-key="id"
              handle=".config-drag-handle"
              tag="div"
              :animation="150"
              ghost-class="ghost-config-item"
          >
              <template #item="{ element: configCard }">
                   <div class="config-item">
                       <span class="config-drag-handle" style="cursor: move; margin-right: 8px;">☰</span>
                       <ElCheckbox v-model="configCard.visible" :label="configCard.title" size="large"/>
                   </div>
              </template>
          </draggable>

         <template #footer>
           <span class="dialog-footer">
             <ElButton @click="cancelConfigChanges">取消</ElButton>
             <ElButton type="danger" @click="clearLayoutConfig">重置布局</ElButton>
             <ElButton type="primary" @click="applyConfigChanges">应用</ElButton>
           </span>
         </template>
       </ElDialog>

    </main>
    
    <!-- 生产状态弹窗 -->
    <ProductionStatusDialog
      v-model="statusDialogVisible"
      :data="statusDialogData"
      :start-date="startDateForDialog"
      :end-date="endDateForDialog"
    />
    
    <!-- 高不良率工单明细弹窗 -->
    <ElDialog
      v-model="highDefectDialogVisible"
      title="不良率偏高工单明细"
      width="800px"
      class="high-defect-dialog"
      destroy-on-close
    >
      <div class="high-defect-detail">
        <table class="high-defect-table">
          <thead>
            <tr>
              <th>工单号</th>
              <th>不良率(%)</th>
              <th>型号</th>
              <th>工厂</th>
              <th>生产日期</th>
              <th>不良/检验</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="order in highDefectWorkOrders" :key="order.id">
              <td><strong>{{ order.id }}</strong></td>
              <td><span class="defect-rate">{{ order.rate }}%</span></td>
              <td>{{ order.model || '-' }}</td>
              <td>{{ order.factory || '-' }}</td>
              <td>{{ order.productionDate || '-' }}</td>
              <td>{{ order.defectCount || 0 }}/{{ order.inspectionCount || 0 }}</td>
              <td>
                <ElButton 
                  type="primary" 
                  size="small" 
                  @click="openProductionStatusDialog(order)"
                >
                  查看详情
                </ElButton>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <ElButton @click="highDefectDialogVisible = false">关闭</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 工厂对比弹窗 -->
    <FactoryComparisonDialog
      v-model:visible="factoryComparisonDialogVisible"
      :metric-type="currentMetricType"
      :title="currentMetricTitle"
      @openProductionLine="handleOpenProductionLine"
    />
    
    <!-- 生产线详情弹窗 -->
    <ProductionLineDialog
      v-model:visible="productionLineDialogVisible"
      :fact-no="currentFactoryNo"
      :fact-name="currentFactoryName"
    />

    <!-- AI摘要按钮组件 -->
    <AISummaryButton
      :data-fetcher="getDashboardDataForAI"
      position="bottom-right"
      @summary-generated="handleSummaryGenerated"
      @summary-error="handleSummaryError"
    />
  </div>
</template>

<style lang="scss">
:root {
  --color-primary: #3498DB;
  --color-primary-light: #ebf5fb;
  --color-text-primary: #303133;
  --color-text-regular: #606266;
  --color-text-secondary: #909399;
  --color-border-base: #DCDFE6;
  --color-border-light: #E4E7ED;
  --color-border-lighter: #EBEEF5;
  --color-border-extra-light: #F2F6FC;
  --color-background-base: #F5F7FA;
  --color-white: #FFFFFF;
  --color-kpi-orange: #F39C12;
  --color-kpi-purple: #8E44AD;
  --color-kpi-blue: var(--color-primary);
  --color-kpi-green: #2ECC71;
  --color-border-kpi-orange: #FADCAA;
  --color-border-kpi-purple: #D8BFDE;
  --color-border-kpi-blue: #A9D6F5;
  --color-border-kpi-green: #A3E4B9;
  --color-urgent: #E74C3C;
  --color-overdue: var(--color-kpi-orange);
  --color-pending: #95A5A6;
  --color-progress: var(--color-primary);
  --color-completed: var(--color-kpi-green);

  --card-padding: 15px;
  --card-border-radius: 4px;
  --grid-gap: 15px;
}

body {
  margin: 0;
  font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "\5FAE\8F6F\96C5\9ED1", Arial, sans-serif;
  color: var(--color-text-primary);
  background-color: var(--color-background-base);
}

.dashboard-layout {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--grid-gap);
  min-height: calc(100vh - 2 * var(--grid-gap));
}

.dashboard-topbar {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--grid-gap);
  
  .grid-card {
    min-height: auto;
  }
  
  .search-filter-wrapper {
    grid-column: span 2;
    padding: 0;
    
    .search-controls {
      display: flex;
      align-items: center;
      gap: 10px;
      flex-wrap: wrap;

      .config-button {
        margin-left: auto;
      }
    }
  }

  .common-functions-section {
    grid-column: span 2;
    padding: 0;
    
    .function-items {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      width: 100%;
      justify-content: flex-start;
    }
  }
}

.search-label {
  color: var(--color-text-regular);
  font-size: 14px;
  margin-right: 5px;
}

.header-search-input .el-input__wrapper,
.header-select .el-input__wrapper {
  border-radius: var(--card-border-radius);
  box-shadow: none;
  border: 1px solid var(--color-border-base);
}
.header-search-input .el-input__wrapper:hover,
.header-select .el-input__wrapper:hover {
  border-color: var(--color-text-secondary);
}
.header-search-input .el-input__wrapper.is-focus,
.header-select .el-input__wrapper.is-focus {
   border-color: var(--color-primary);
}

.header-button-group .el-button {
   margin: 0 !important;
   border-radius: 0;
}
.header-button-group .el-button:first-child {
   border-top-left-radius: var(--card-border-radius);
   border-bottom-left-radius: var(--card-border-radius);
}
.header-button-group .el-button:last-child {
   border-top-right-radius: var(--card-border-radius);
   border-bottom-right-radius: var(--card-border-radius);
}

.header-button-group .el-button--default {
  border-color: var(--color-border-base);
  background-color: var(--color-white);
  color: var(--color-text-regular);
}
.header-button-group .el-button--default:hover {
  border-color: var(--color-primary-light);
  background-color: var(--color-primary-light);
  color: var(--color-primary);
}

.section-title {
  color: var(--color-text-regular);
  font-size: 14px;
  white-space: nowrap;
}

.function-items {
  display: flex;
  gap: 15px;
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  color: var(--color-text-regular);
  font-size: 13px;
  text-align: center;
}

.function-item:hover {
  color: var(--color-primary);
}

.placeholder-icon {
   display: inline-flex;
   align-items: center;
   justify-content: center;
   width: 32px;
   height: 32px;
   background-color: var(--color-border-light);
   color: var(--color-text-secondary);
   font-size: 18px;
   border-radius: 4px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  grid-auto-rows: minmax(300px, auto);
  gap: var(--grid-gap);
  flex-grow: 1;
}

.grid-card {
  background-color: var(--color-white);
  border-radius: 8px;
  border: 1px solid var(--color-border-light);
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.03);
  display: flex;
  flex-direction: column;
  min-height: 150px;
  overflow: hidden;
  position: relative;

  &.card-kpiSummary {
    background-color: transparent;
    border: none;
    box-shadow: none;
    padding: 0;

    .card-inner-content {
      padding: 0;
    }
  }
}

.card-inner-content {
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    padding: var(--card-padding);
    height: 100%;
    box-sizing: border-box;
}

.card-title-inner {
    font-size: 16px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 0;
    padding: 4px 0 8px 0;
    width: 100%;
    text-align: left;
    box-sizing: border-box;
    flex-shrink: 0;
    border-bottom: 1px solid var(--color-border-lighter);
    margin-bottom: 8px;
}

.grid-card[class*="chart"] {
  padding: 0;
}
.chart-instance {
    width: 100%;
    flex-grow: 1;
    min-height: 100px;
    overflow: hidden;
}

.grid-card[class*="list"] {
  padding: 0;
}
.list-card .card-inner-content {
  padding-top: 0;
}
.list-card .card-content-scrollable {
  flex-grow: 1;
  height: 0;
  padding: 0;
  margin-top: 8px;
}

.grid-card[class*="status"] {
    padding: 0;
}
.status-card .card-inner-content {
  padding-top: 0;
}
.card-content-static {
    padding: 0;
    margin-top: 8px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.issue-status-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}
.status-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--color-border-lighter);
    padding: 5px 10px;
    font-size: 14px;
    color: var(--color-text-regular);
}
.ghost-card {
  opacity: 0.5;
  background: #c8ebfb;
  border: 1px dashed #409EFF;
}
.drag-handle {
  background-color: rgba(0,0,0,0.02);
  border-bottom: 1px dashed rgba(0,0,0,0.05);
  &:hover {
      background-color: rgba(0,0,0,0.05);
  }
}

.config-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--color-border-lighter);
    &:last-child {
        border-bottom: none;
    }
    .el-checkbox {
        flex-grow: 1;
    }
}
.ghost-config-item {
    opacity: 0.5;
    background: var(--color-border-lighter);
}

/* 为移动后的配置按钮添加全局样式 */
.config-button {
  position: absolute;
  top: calc(var(--grid-gap) - 30px); /* 向上移动8px */
  right: calc(var(--grid-gap) - 20px); /* 向右移动8px */
  z-index: 10; /*确保在其他元素之上*/
}

.high-defect-workorders {
  margin-top: 10px;
  font-size: 13px;
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  flex-wrap: wrap; // 允许换行

  .workorder-item {
    color: var(--color-text-regular); // 工单本身颜色深一些
    margin-right: 2px; // 与分隔符的间距
  }
  .workorder-item-link {
    color: var(--color-primary);
    text-decoration: none;
    cursor: pointer;
    margin-right: 2px;
    &:hover {
      text-decoration: underline;
    }
  }
  .separator {
    margin-right: 5px; // 分隔符与下一个工单的间距
  }
  .more-link {
    color: var(--color-primary);
    text-decoration: none;
    margin-left: 5px;
    &:hover {
      text-decoration: underline;
    }
  }
  
  .disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  .no-data {
    font-style: italic;
    color: var(--color-text-secondary);
  }
}

/* 高不良率工单表格样式 */
.high-defect-detail {
  max-height: 500px;
  overflow-y: auto;
}

.high-defect-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  font-size: 14px;
  
  th, td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid var(--color-border-light);
  }
  
  th {
    background-color: var(--color-background-base);
    font-weight: 600;
    color: var(--color-text-regular);
    position: sticky;
    top: 0;
    z-index: 1;
  }
  
  tr:hover {
    background-color: var(--color-primary-light);
  }
  
  .defect-rate {
    font-weight: bold;
    color: #E74C3C;
  }
}

/* 高不良率工单弹窗样式 */
.high-defect-dialog {
  .el-dialog__header {
    background-color: var(--color-primary);
    margin-right: 0;
    padding: 15px 20px;
    
    .el-dialog__title {
      color: white;
      font-size: 16px;
      font-weight: 600;
    }
    
    .el-dialog__headerbtn .el-icon {
      color: white;
    }
  }
  
  .el-dialog__body {
    padding: 15px 20px;
  }
}
</style>
