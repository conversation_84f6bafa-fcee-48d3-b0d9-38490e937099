import { JwtPayload } from './types'
import { useCache } from '@/hooks/web/useCache'

const { wsCache } = useCache()

/**
 * JWT令牌解析
 * @param token JWT令牌
 * @returns 解析后的JWT载荷
 */
export const parseJwt = (token: string): JwtPayload => {
  try {
    const base64Url = token.split('.')[1]
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/')
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(function(c) {
          return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
        })
        .join('')
    )
    return JSON.parse(jsonPayload)
  } catch (e) {
    console.error('解析token失败:', e)
    return {}
  }
}

/**
 * 格式化日期
 * @param dateStr 日期字符串或Date对象
 * @returns 格式化后的日期字符串
 */
export const formatDate = (dateStr?: string | Date): string => {
  if (!dateStr) return ''
  
  const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr
  
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
}

/**
 * 保存用户信息到本地存储
 * @param payload JWT解析的用户信息
 * @param token 访问令牌
 */
export const saveUserInfo = (payload: JwtPayload, token: string): void => {
  // 保存令牌
  wsCache.set('token', token)
}

/**
 * 清除用户信息
 */
export const clearUserInfo = (): void => {
  wsCache.delete('token')
}

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export const isLoggedIn = (): boolean => {
  const token = wsCache.get('token')
  if (!token) return false
  
  try {
    const payload = parseJwt(token)
    // 检查令牌是否过期
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      clearUserInfo()
      return false
    }
    return true
  } catch (e) {
    clearUserInfo()
    return false
  }
} 