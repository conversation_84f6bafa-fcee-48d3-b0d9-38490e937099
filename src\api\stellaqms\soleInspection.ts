import request from '@/config/axios'

// 定义返回类型接口
export interface PageResult<T> {
  list: T[]
  total: number
}

// 搜索VO
export interface SoleInspectionPageReqVO {	
	pageNo: number
	pageSize: number
	factNo?: string
	menuId?: number
	purNo?: string
	startTime?: string
	endTime?: string
	// dateRange?: string[]
}

// 检验资料VO
export interface SoleInspectionRespVO {	
	dataId: string
	factNo: string
	menuId: number
	purNo: string
	modelNo: string
	brandNo: string
	vndSim: string
	orderNo: string
	puom: string
	onlineDate: string
	outDate: string
	itemNo: string
	matSeq: string
	matDesc: string
	purQty: string
	addUser: string
	addDate: string
	toolingCode: string
	endStatus: string
	deleteStatus: string
	inspectSum: string
	passSum: string
	defectSum: string
	checkStatus: string
	userid: number
	username: string
	menuName: string
	menuEname: string
	menuVname: string
	menuIname: string	  
}

// 审核信息响应VO
export interface SoleInspectionAuditRespVO {
	dataId: string
	factNo: string
	addDatestr: string
	purNo: string
	itemNo: string
	brandNo: string
	vndSim: string
	orderNo: string
	matSeq: string
	matDesc: string
	purQty: string
	modelNo: string
	onlineDate: string
	outDate: string
	checkStatus: string
	username: string
	menuName: string
	menuEname: string
	menuVname: string
	menuIname: string
}

// 检验详情响应VO
export interface SoleInspectionDetailRespVO {
	sizeId: string
	sizeName: string
	purQty: number
	inspectPair: number
	leftInspectTotal: number
	leftPass: number
	leftFail: number
	leftCount: number
	rightPass: number
	rightFail: number
	rightCount: number
	needSupply: number
}

// 外观检验详情响应VO
export interface SoleAppearanceInspectionRespVO {
	sizeId: string
	sizeName: string
	purQty: number
	inspectPair: number
	passPair: number
	defectPair: number
}

// 问题点响应VO
export interface SoleDefectRespVO {
	soleDefectId: string
	soleDefectName: string
	soleDefectEname: string
	soleDefectVname: string
	soleDefectIname: string
}

// 问题点详情响应VO
export interface SoleDefectDetailRespVO {
	sizeId: string
	sizeName: string
	leftInspect: number
	rightInspect: number
}

// 菜单VO
export interface SoleMenuRespVO {
	menuId: number
	menuName: string
	menuVname: string
	menuIname: string
}


// 获取半成品检验分页
export const getSoleInspectionPage = (params: SoleInspectionPageReqVO) => {
  return request.get<PageResult<SoleInspectionRespVO>>({
    url: '/stella-qms/sole-inspection/page',
    params
  })
}

// 获取工厂列表
export const getFactoryList = () => {
  return request.get<string[]>({
    url: '/stella-qms/sole-inspection/factory-list'
  })
}

// 获取菜单列表
export const getMenuList = () => {
  return request.get<SoleMenuRespVO[]>({
    url: '/stella-qms/sole-inspection/menu-list'
  })
}


// 获取审核信息
export const getAuditInfo = (dataId: string) => {
  return request.get<SoleInspectionAuditRespVO>({
    url: '/stella-qms/sole-inspection/audit-info',
    params: { dataId }
  })
}

// 获取检验详情数据
export const getInspectionDetail = (dataId: string) => {
  return request.get<SoleInspectionDetailRespVO[]>({
    url: '/stella-qms/sole-inspection/inspection-detail',
    params: { dataId }
  })
}

// 获取外观检验详情数据
export const getAppearanceInspectionDetail = (dataId: string, factNo: string) => {
  return request.get<SoleAppearanceInspectionRespVO[]>({
    url: '/stella-qms/sole-inspection/appearance-inspection-detail',
    params: { dataId, factNo }
  })
}

// 获取问题点列表
export const getDefectList = (factNo: string) => {
  return request.get<SoleDefectRespVO[]>({
    url: '/stella-qms/sole-inspection/defect-list',
    params: { factNo }
  })
}

// 获取问题点对应尺码数量
export const getDefectDetail = (dataId: string, soleDefectId: string, factNo: string) => {
  return request.get<SoleDefectDetailRespVO[]>({
    url: '/stella-qms/sole-inspection/defect-detail',
    params: { dataId, soleDefectId, factNo }
  })
}

// 审核半成品检验
export const approveInspection = (dataId: string) => {
  return request.post<boolean>({
    url: '/stella-qms/sole-inspection/approve',
    params: { dataId }
  })
}
