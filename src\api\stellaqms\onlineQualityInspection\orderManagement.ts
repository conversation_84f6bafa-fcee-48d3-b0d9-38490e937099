import request from '@/config/axios'

/**
 * 订单管理 API 接口
 */

// 获取订单列表
export const getOrderList = (params: OrderPageReqVO) => {
  return request.get({ url: '/stellaqms/onlineQualityInspection/page', params })
}

// 导出订单数据
export const exportOrderData = (params: OrderPageReqVO) => {
  return request.download({
    url: '/stellaqms/order/export',
    params
  })
}

// 获取订单详情
export const getOrderDetail = (id: number) => {
  return request.get({ url: `/stellaqms/onlineQualityInspection/get?id=${id}` })
}

// 获取不良率詳情
export function getDefectInfo(factNo: string, orderNo: string, startDate: string, endDate: string, custPo: string) {
  return request.get({
    url: '/stellaqms/onlineQualityInspection/defect-info',
    params: {
      factNo,
      orderNo,
      startDate,
      endDate,
      custPo
    }
  })
}

// 获取测试详情
export const getTestingDetail = (deptId,orderNo, inspType, factNo,startDate,endDate ) => {
  // 构建查询参数对象
  const queryParams = {
    deptId,
    orderNo,
    inspType,
    factNo,
    startDate,
    endDate
  };

  // 使用模板字符串构建完整的URL
  const url = `/stellaqms/onlineQualityInspection/getTestingDetail?${new URLSearchParams(queryParams)}`;

  // 发送GET请求
  return request.get({ url });
};

/**
 * 订单分页查询请求 VO
 */
export interface OrderPageReqVO {
  pageNo: number
  pageSize: number
  factNo?: string
  brandName?: string
  styleNo?: string
  workOrderNo?: string
  poNo?: string
  beginTime?: string
  endTime?: string
}

/**
 * 订单信息 VO
 */
export interface OrderRespVO {
  id: number
  factNo: string
  deptId: string
  inspType: string
  modelNo: string
  brandName: string
  colorName: string
  selectDate: string
  teamId: string
  deptName: string
  deptEname: string
  deptVname: string
  deptIname: string
  orderNo: string
  custPo: string
  ordQty: number
  blNum: number
  timeNum: number
  teamName: string
  teamJc: string
  deptCode: string
  deptDesc: string
}

/**
 * QA问题列表查询请求 VO
 */
export interface QaIssueByOrderReqVO {
  orderNumber: string
  pageNo?: number
  pageSize?: number
}

/**
 * QA问题信息 VO
 */
export interface QaIssueVO {
  id: number
  issueNo: string
  brand: string
  factory: string
  issueRemark: string
  model: string
  orderNumber: string
  status: number
  publishDate: string
}

/**
 * 根据订单号获取QA问题列表
 */
export const getQaIssuesByOrder = (orderNumber: string) => {
  return request.get<QaIssueVO[]>({ 
    url: '/stellaqms/onlineQualityInspection/qa-issues-by-order', 
    params: { orderNumber } 
  })
}

/**
 * 获取QA问题统计数量
 */
export const getQaIssuesCount = (orderNumber: string) => {
  return request.get<number>({ 
    url: '/stellaqms/onlineQualityInspection/qa-issues-count', 
    params: { orderNumber } 
  })
}
