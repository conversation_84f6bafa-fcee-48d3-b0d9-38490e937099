import { useCache } from '@/hooks/web/useCache'
import axios from 'axios'
import {
  UserLoginVO,
  TokenVO,
  RoleVO,
  RolePageReqVO,
  UserVO,
  UserPageReqVO,
  PermissionVO,
  PermissionFormVO,
  PermissionQueryVO,
  DataScopeVO,
  PageResult,
  ResetPasswordVO,
  DepartmentRoleDTO,
  DepartmentRoleVO,
  IssueRolePersonsVO,
  BatchSaveDepartmentRoleRequest,
  DeptVO
} from './types'
import { parseJwt, saveUserInfo, clearUserInfo, isLoggedIn, formatDate } from './utils'

// 导出工具函数和类型，让用户可以直接从api模块导入所有内容
export * from './types'
export { parseJwt, saveUserInfo, clearUserInfo, isLoggedIn, formatDate } from './utils'

const { wsCache } = useCache()

// 接口基础URL
const BASE_URL = import.meta.env.VITE_QA_APP_BASE_URL

// 创建请求实例
const request = async <T>(options: any): Promise<T> => {
  const { url, method, data, params } = options
  
  // 获取token
  const token = wsCache.get('token')
  
  try {
    const response = await axios({
      baseURL: BASE_URL,
      url,
      method,
      data,
      params,
      headers: token ? { 'Authorization': `Bearer ${token}` } : {}
    })
    
    if (response.data.code !== 1) {
      return Promise.reject(response.data)
    }
    
    return response.data
  } catch (error) {
    return Promise.reject(error)
  }
}

/**
 * 用户登录
 * @param data 登录信息
 */
export const login = async (data: UserLoginVO): Promise<{ data: TokenVO }> => {
  const res = await request<{ data: TokenVO }>({
    url: '/auth/login',
    method: 'POST',
    data
  })
  
  // 登录成功后自动保存Token和用户信息
  if (res.data && res.data.token) {
    const token = res.data.token
    const userInfo = parseJwt(token)
    saveUserInfo(userInfo, token)
  }
  
  return res
}

/**
 * 获取当前用户信息
 */
export const getCurrentUser = async (): Promise<{ data: UserVO }> => {
  return request({
    url: '/security/user/current',
    method: 'GET'
  })
}

/**
 * 获取角色分页列表
 * @param params 查询参数
 */
export const getRolePage = async (params: RolePageReqVO): Promise<{ data: PageResult<RoleVO> }> => {
  return request({
    url: '/security/role/page',
    method: 'GET',
    params
  })
}

/**
 * 获取角色详情
 * @param id 角色ID
 */
export const getRole = async (id: number): Promise<{ data: RoleVO }> => {
  return request({
    url: `/security/role/${id}`,
    method: 'GET'
  })
}

/**
 * 创建角色
 * @param data 角色信息
 */
export const createRole = async (data: RoleVO) => {
  return request({
    url: '/security/role',
    method: 'POST',
    data
  })
}

/**
 * 更新角色
 * @param data 角色信息
 */
export const updateRole = async (data: RoleVO) => {
  return request({
    url: '/security/role',
    method: 'PUT',
    data
  })
}

/**
 * 删除角色
 * @param id 角色ID
 */
export const deleteRole = async (id: number) => {
  return request({
    url: `/security/role/${id}`,
    method: 'DELETE'
  })
}

/**
 * 更新角色状态
 * @param id 角色ID
 * @param status 状态值
 */
export const updateRoleStatus = async (id: number, status: number) => {
  return request({
    url: `/security/role/status/${id}/${status}`,
    method: 'PUT'
  })
}

/**
 * 分配角色权限
 * @param roleId 角色ID
 * @param permissionIds 权限ID数组
 */
export const assignRolePermissions = async (roleId: number, permissionIds: number[]) => {
  return request({
    url: `/security/role/permissions/${roleId}`,
    method: 'PUT',
    data: permissionIds
  })
}

/**
 * 获取权限树
 */
export const getPermissionTree = async (): Promise<{ data: PermissionVO[] }> => {
  return request({
    url: '/security/permission/tree',
    method: 'GET'
  })
}

/**
 * 创建权限
 * @param data 权限信息
 */
export const createPermission = async (data: PermissionFormVO) => {
  return request({
    url: '/security/permission',
    method: 'POST',
    data
  })
}

/**
 * 更新权限
 * @param data 权限信息
 */
export const updatePermission = async (data: PermissionFormVO) => {
  return request({
    url: '/security/permission',
    method: 'PUT',
    data
  })
}

/**
 * 删除权限
 * @param id 权限ID
 */
export const deletePermission = async (id: number) => {
  return request({
    url: `/security/permission/${id}`,
    method: 'DELETE'
  })
}

/**
 * 更新权限状态
 * @param id 权限ID
 * @param status 状态值
 */
export const updatePermissionStatus = async (id: number, status: number) => {
  return request({
    url: `/security/permission/status/${id}/${status}`,
    method: 'PUT'
  })
}

/**
 * 获取用户分页列表
 * @param params 查询参数
 */
export const getUserPage = async (params: UserPageReqVO): Promise<{ data: PageResult<UserVO> }> => {
  return request({
    url: '/security/user/page',
    method: 'GET',
    params
  })
}

/**
 * 获取用户详情
 * @param id 用户ID
 */
export const getUser = async (id: number): Promise<{ data: UserVO }> => {
  return request({
    url: `/security/user/${id}`,
    method: 'GET'
  })
}

/**
 * 创建用户
 * @param data 用户信息
 */
export const createUser = async (data: UserVO) => {
  return request({
    url: '/security/user',
    method: 'POST',
    data
  })
}

/**
 * 更新用户
 * @param data 用户信息
 */
export const updateUser = async (data: UserVO) => {
  return request({
    url: '/security/user',
    method: 'PUT',
    data
  })
}

/**
 * 删除用户
 * @param id 用户ID
 */
export const deleteUser = async (id: number) => {
  return request({
    url: `/security/user/${id}`,
    method: 'DELETE'
  })
}

/**
 * 更新用户状态
 * @param id 用户ID
 * @param status 状态值
 */
export const updateUserStatus = async (id: number, status: number) => {
  return request({
    url: `/security/user/status/${id}/${status}`,
    method: 'PUT'
  })
}

/**
 * 分配用户角色
 * @param userId 用户ID
 * @param roleIds 角色ID数组
 */
export const assignUserRoles = async (userId: number, roleIds: number[]) => {
  return request({
    url: `/security/user/roles/${userId}`,
    method: 'PUT',
    data: roleIds
  })
}

/**
 * 获取数据权限
 * @param roleId 角色ID
 */
export const getDataScope = async (roleId: number): Promise<{ data: DataScopeVO }> => {
  return request({
    url: `/security/datascope/${roleId}`,
    method: 'GET'
  })
}

/**
 * 设置数据权限
 * @param data 数据权限信息
 */
export const setDataScope = async (data: DataScopeVO) => {
  return request({
    url: '/security/datascope',
    method: 'PUT',
    data
  })
}

/**
 * 获取可用工厂列表
 */
export const getFactoryList = async (): Promise<{ data: [] }> => {
  return request({
    url: '/security/datascope/factories',
    method: 'GET'
  })
}

/**
 * 获取可用品牌列表
 */
export const getBrandList = async (): Promise<{ data: [] }> => {
  return request({
    url: '/security/datascope/brands',
    method: 'GET'
  })
}

/**
 * 获取部门列表
 * @param factoryId 可选的工厂ID
 */
export const getDeptList = async (factoryId?: string): Promise<{ data: DeptVO[] }> => {
  return request({
    url: factoryId ? `/department/list?factory=${factoryId}` : '/department/list',
    method: 'GET'
  })
}

/**
 * 重置用户密码
 * @param userId 用户ID
 * @param data 密码信息
 */
export const resetUserPassword = async (userId: number, data: ResetPasswordVO) => {
  return request({
    url: `/security/user/reset-password/${userId}`,
    method: 'PUT',
    data
  })
}

/**
 * 获取所有角色列表
 */
export const getAllRoles = async (): Promise<{ data: RoleVO[] }> => {
  return request({
    url: '/security/roles',
    method: 'GET'
  })
}

/**
 * 获取角色的数据权限设置
 * @param roleId 角色ID
 */
export const getRoleDataScope = async (roleId: number): Promise<{ data: DataScopeVO }> => {
  return request({
    url: `/security/datascope/role/${roleId}`,
    method: 'GET'
  })
}

/**
 * 保存数据权限设置
 * @param data 数据权限设置
 */
export const saveDataScope = async (data: DataScopeVO) => {
  return request({
    url: `/security/datascope/role/${data.roleId}`,
    method: 'POST',
    data
  })
}

/**
 * 保存部门角色关联
 * @param data 部门角色关联信息
 */
export const saveDepartmentRole = async (data: DepartmentRoleDTO) => {
  return request({
    url: '/security/department-role',
    method: 'POST',
    data
  })
}

/**
 * 批量保存部门角色关联
 * @param data 批量保存请求数据
 */
export const batchSaveDepartmentRoles = async (data: BatchSaveDepartmentRoleRequest) => {
  return request({
    url: '/security/department-role/batch',
    method: 'POST',
    data
  })
}

/**
 * 获取部门角色关联信息
 * @param factoryId 工厂ID
 * @param departmentId 部门ID
 */
export const getDepartmentRoles = async (factoryId: string, departmentId: number): Promise<{ data: DepartmentRoleVO[] }> => {
  return request({
    url: `/security/department-role/${factoryId}/${departmentId}`,
    method: 'GET'
  })
}

/**
 * 获取工厂的所有部门角色信息
 * @param factoryId 工厂ID
 */
export const getFactoryDepartmentRoles = async (factoryId: string): Promise<{ data: DepartmentRoleVO[] }> => {
  return request({
    url: `/security/department-role/factory/${factoryId}`,
    method: 'GET'
  })
}

/**
 * 删除部门角色关联
 * @param id 部门角色关联ID
 */
export const deleteDepartmentRole = async (id: number) => {
  return request({
    url: `/security/department-role/${id}`,
    method: 'DELETE'
  })
}

/**
 * 删除部门的特定角色关联
 * @param factoryId 工厂ID
 * @param departmentId 部门ID
 * @param roleType 角色类型
 */
export const deleteDepartmentRoleByType = async (factoryId: string, departmentId: number, roleType: number) => {
  return request({
    url: `/security/department-role/${factoryId}/${departmentId}/${roleType}`,
    method: 'DELETE'
  })
}

/**
 * 删除部门的所有角色关联
 * @param factoryId 工厂ID
 * @param departmentId 部门ID
 */
export const deleteAllDepartmentRoles = async (factoryId: string, departmentId: number) => {
  return request({
    url: `/security/department-role/${factoryId}/${departmentId}`,
    method: 'DELETE'
  })
}

/**
 * 获取问题处理所需的角色人员信息
 * @param factoryId 工厂ID
 * @param departmentId 部门ID
 */
export const getIssueRolePersons = async (factoryId: string, departmentId: number): Promise<{ data: IssueRolePersonsVO }> => {
  return request({
    url: `/security/department-role/issue-roles/${factoryId}/${departmentId}`,
    method: 'GET'
  })
} 