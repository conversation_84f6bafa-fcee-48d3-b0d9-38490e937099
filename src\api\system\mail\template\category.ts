import request from '@/config/axios'

export interface MailTemplateCategoryVO {
  id?: number
  name: string
  code: string
  description?: string
  sortOrder?: number
  status: number
  createTime?: Date
}

// 查询邮件模板分类列表
export const getMailTemplateCategoryList = async () => {
  return await request.get({ url: '/system/mail-template-category/list' })
}

// 查询启用状态的邮件模板分类精简列表
export const getSimpleMailTemplateCategoryList = async () => {
  return await request.get({ url: '/system/mail-template-category/simple-list' })
}

// 查询邮件模板分类详情
export const getMailTemplateCategory = async (id: number) => {
  return await request.get({ url: '/system/mail-template-category/get?id=' + id })
}

// 新增邮件模板分类
export const createMailTemplateCategory = async (data: MailTemplateCategoryVO) => {
  return await request.post({ url: '/system/mail-template-category/create', data })
}

// 修改邮件模板分类
export const updateMailTemplateCategory = async (data: MailTemplateCategoryVO) => {
  return await request.put({ url: '/system/mail-template-category/update', data })
}

// 删除邮件模板分类
export const deleteMailTemplateCategory = async (id: number) => {
  return await request.delete({ url: '/system/mail-template-category/delete?id=' + id })
} 