<template>
  <div class="qip-audit-component">
    <!-- 图表容器 -->
    <div class="chart-container" v-loading="loading">
      <div ref="chartRef" class="chart-content"></div>
      <div v-if="qipData.length === 0" class="no-data">
        暂无QIP稽核数据
      </div>
    </div>

    <!-- 图表标题和说明 -->
    <div class="chart-footer" v-if="qipData.length > 0">
      <div class="chart-title">各工厂最新一次的稽核分数</div>
      <div class="chart-subtitle">点击柱状图查看该工厂稽核评细信息</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { getQipAuditData, type QipAuditVO } from '@/api/stellaqms/dashboard'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts/core'
import { BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'
import type { EChartsType } from 'echarts/core'

// 注册必要的组件
echarts.use([
  BarChart,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  CanvasRenderer
])

defineOptions({ name: 'QipAuditComponent' })

interface Props {
  colors?: Record<string, string>
  title?: string
  dateStr?: string
}

const props = withDefaults(defineProps<Props>(), {
  colors: () => ({}),
  title: 'QIP稽核'
})

const loading = ref(false)
const qipData = ref<QipAuditVO[]>([])
const chartRef = ref<HTMLElement>()
let chart: EChartsType | null = null

// 获取评分对应的颜色
const getScoreColor = (score: number | undefined): string => {
  if (!score) return '#999999'
  if (score >= 90) return '#2ECC71'  // 绿色 - 优秀
  if (score >= 80) return '#3498DB'  // 蓝色 - 良好
  if (score >= 70) return '#F39C12'  // 橙色 - 一般
  if (score >= 60) return '#E67E22'  // 深橙色 - 警告
  return '#E74C3C'  // 红色 - 危险
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return

  // 销毁旧图表
  if (chart) {
    chart.dispose()
  }

  // 创建新图表
  chart = echarts.init(chartRef.value)

  // 更新图表
  updateChart()

  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', resizeChart)
}

// 调整图表大小
const resizeChart = () => {
  chart?.resize()
}

// 更新图表数据
const updateChart = () => {
  if (!chart || qipData.value.length === 0) return

  // 按分数降序排序
  const sortedData = [...qipData.value].sort((a, b) => (b.monthlyScore || 0) - (a.monthlyScore || 0))

  // 准备图表数据
  const factories = sortedData.map(item => item.factory || '未知工厂')
  const scores = sortedData.map(item => item.monthlyScore || 0)
  const colors = scores.map(score => getScoreColor(score))

  const option = {
    grid: {
      left: '10%',
      right: '5%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: factories,
      axisLabel: {
        interval: 0,
        rotate: 0,
        fontSize: 12,
        color: '#666'
      },
      axisTick: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#e0e0e0'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 82,
      max: 96,
      interval: 2,
      axisLabel: {
        fontSize: 12,
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'solid'
        }
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params: any) {
        const data = params[0]
        const dataIndex = data.dataIndex
        const item = sortedData[dataIndex]
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 4px;">${item.factory}</div>
            <div>月度評分: <span style="color: ${getScoreColor(item.monthlyScore)}; font-weight: bold;">${item.monthlyScore?.toFixed(1) || '0.0'}分</span></div>
            <div>優先等級: ${item.mainType || '未知'}</div>
            <div>責任人: ${item.responsibleManagers || '未知'}</div>
          </div>
        `
      },
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'rgba(255, 255, 255, 0.3)',
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        type: 'bar',
        data: scores.map((score, index) => ({
          value: score,
          itemStyle: {
            color: colors[index]
          }
        })),
        barWidth: '60%',
        itemStyle: {
          borderRadius: [4, 4, 0, 0]
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        }
      }
    ]
  }

  chart.setOption(option)
}

// 获取QIP稽核数据
const fetchQipAuditData = async () => {
  loading.value = true
  try {
    const res = await getQipAuditData(props.dateStr)
    qipData.value = res || []
    // 数据更新后重新渲染图表
    if (qipData.value.length > 0) {
      await nextTick()
      if (!chart) {
        initChart()
      } else {
        updateChart()
      }
    }
  } catch (error) {
    console.error('获取QIP稽核数据失败:', error)
    ElMessage.error('获取QIP稽核数据失败')
    qipData.value = []
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  await fetchQipAuditData()
})

onUnmounted(() => {
  if (chart) {
    chart.dispose()
  }
  window.removeEventListener('resize', resizeChart)
})

// 监听数据变化
watch(() => qipData.value, () => {
  if (qipData.value.length > 0) {
    nextTick(() => {
      if (!chart) {
        initChart()
      } else {
        updateChart()
      }
    })
  }
}, { deep: true })

// 暴露刷新方法
defineExpose({
  refresh: fetchQipAuditData
})
</script>

<style lang="scss" scoped>
.qip-audit-component {
  height: 100%;
  display: flex;
  flex-direction: column;
  font-size: 14px;
}

.chart-container {
  flex: 1;
  position: relative;
  min-height: 300px;

  .chart-content {
    width: 100%;
    height: 100%;
    min-height: 300px;
  }

  .no-data {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: var(--color-text-secondary);
    font-style: italic;
    font-size: 16px;
  }
}

// 图表底部信息
.chart-footer {
  margin-top: 10px;
  text-align: center;
  flex-shrink: 0;

  .chart-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 4px;
  }

  .chart-subtitle {
    font-size: 12px;
    color: var(--color-text-secondary);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chart-container {
    min-height: 250px;

    .chart-content {
      min-height: 250px;
    }
  }

  .chart-footer {
    .chart-title {
      font-size: 13px;
    }

    .chart-subtitle {
      font-size: 11px;
    }
  }
}
</style> 