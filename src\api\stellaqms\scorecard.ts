import request from '@/config/axios'




export interface listReqVo {
  sDate: string
}



export interface jqDetailVo {
  sDate: string
  factory:string
}

export type updateScoreVo = Array<{
  typebId:number
  score:number
  ssValue:string
}>;






// 配置信息 http://localhost:48080/admin-api/stella-qms/scorecard/config
export const getConfigInfo = () => {
  return request.get({ url: '/stella-qms/scorecard/config'})
}

export const getConfigSet = () => {
  return request.get({ url: '/stella-qms/scorecard/getScoreSet'})
}
//  http://localhost:48080/admin-api/stella-qms/scorecard/updateScoreSet
export const updateScoreSet = (data: updateScoreVo) => {
  return request.post({ url: '/stella-qms/scorecard/updateScoreSet', data })
}



// 表格数据 http://localhost:48080/admin-api/stella-qms/scorecard/indicators?sDate=202506
export const getTableInfo = (params: listReqVo) => {
  return request.get({ url: '/stella-qms/scorecard/indicators', params })
}

export const getJQDetail = (params: jqDetailVo) => {
  return request.get({ url: '/stella-qms/scorecard/getJQDetail', params })
}

export const getJQDetailList = (params: jqDetailVo) => {
  return request.get({ url: '/stella-qms/scorecard/getJQDetailList', params })
}




