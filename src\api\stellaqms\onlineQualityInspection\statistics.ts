import request from '@/config/axios'

/**
 * 在线质量检验统计数据 API 接口
 */

// 获取统计数据
export const getStatistics = (params?: {
  factNo?: string
  brandName?: string
  beginTime?: string
  endTime?: string
}) => {
  return request.get({ url: '/stellaqms/onlineQualityInspection/statistics', params })
}

// 获取前5大问题点
export const getTop5Issues = (params?: {
  factNo?: string
  brandName?: string
  beginTime?: string
  endTime?: string
}) => {
  return request.get({ url: '/stellaqms/onlineQualityInspection/top5-issues', params })
}

// 获取品牌不良率数据
export const getBrandDefectRate = (params?: {
  factNo?: string
  brandName?: string
  beginTime?: string
  endTime?: string
}) => {
  return request.get({ url: '/stellaqms/onlineQualityInspection/brand-defect-rate', params })
}

// 获取部门不良率数据
export const getDepartmentDefectRate = (params?: {
  factNo?: string
  brandName?: string
  beginTime?: string
  endTime?: string
}) => {
  return request.get({ url: '/stellaqms/onlineQualityInspection/department-defect-rate', params })
}

/**
 * 问题项统计信息
 */
export interface IssueStatItem {
  name: string
  value: number
}

/**
 * 品牌不良率数据项
 */
export interface BrandDefectRateItem {
  name: string
  value: number
}

/**
 * 部门不良率数据项
 */
export interface DepartmentDefectRateItem {
  name: string
  data: number[]
}

/**
 * 在线质量检验统计数据 VO
 */
export interface OnlineQualityStatisticsVO {
  factoryCount: number
  brandCount: number
  workOrderCount: number
  styleCount: number
  newStyleCount: number
  oldStyleCount: number
  qaIssueCount: number
  finalInspectionCount: number
  top5Issues: IssueStatItem[]
}
