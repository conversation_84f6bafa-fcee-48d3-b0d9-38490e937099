import request from '@/config/axios'

export interface KpiSummaryVO {
  inspectionPassRate: number
  defectRate: number
  qualityIssues: number
  rectificationRate: number
  todoItems?: number
  myAlerts?: number
}

export interface TopIssueVO {
  name: string
  value: number
}

export interface FactoryDefectRateVO {
  factNo: string
  data: number[]
}

export interface BrandDefectRateVO {
  name: string
  value: number
  inspectionCount?: number
  defectCount?: number
}

export interface QaIssueRankingVO {
  type: string
  count: number
  percentage: number
  rank: number
}

export interface CustomerComplaintVO {
  complaintId: string
  customerName: string
  productName: string
  issueDescription: string
  submitDate: string
  status: string
}

export interface BrandUnboxingRateVO {
  brandName: string
  unboxingRate: number
  inspectionCount: number
  defectCount: number
  brand?: string // 为了兼容后端字段
}

export interface HighDefectWorkOrderVO {
  id: string // 工单号
  rate: number // 不良率
  model: string // 型号
  factory: string // 工厂
  productionDate: string // 生产日期
  defectCount: number // 不良数量
  inspectionCount: number // 检验数量
}

export interface QipAuditVO {
  factNo: string // 工厂编号
  factory: string // 工厂名称
  rating: number // 评分
  dateStr: string // 日期字符串
  mainId: string // 稽核流水号
  selectDate: string // 稽核日期
  mainType: string // 类型
  remark: string // 备注
  mainUnit: string // 稽核单位
  username: string // 建立人
  monthlyScore: number // 月度评分
  priorityLevel: string // 优先级等级
  maxDeductionItem: string // 最大扣分项
  responsibleManagers: string // 责任人列表
}

// 获取首页KPI汇总数据
export const getKpiSummary = () => {
  return request.get<KpiSummaryVO>({ url: '/stella-qms/dashboard/summary' })
}

// 获取前5大问题点
export const getTopIssues = () => {
  return request.get<TopIssueVO[]>({ url: '/stella-qms/dashboard/top-issues' })
}

// 获取工厂不良率数据
export const getFactoryDefectRate = async (): Promise<FactoryDefectRateVO[]> => {
  return await request.get({ url: '/stella-qms/dashboard/factory-defect-rate' })
}

// 获取品牌不良率数据
export const getBrandDefectRate = async (): Promise<BrandDefectRateVO[]> => {
  return await request.get({ url: '/stella-qms/dashboard/brand-defect-rate' })
}

// 获取QA问题排行数据
export const getQaIssueRanking = (factory?: string) => {
  return request.get<QaIssueRankingVO[]>({ 
    url: '/stella-qms/dashboard/qa-issue-ranking',
    params: {
      factory
    }
  })
}

// 获取最新客诉问题数据
export const getCustomerComplaints = () => {
  return request.get<CustomerComplaintVO[]>({ url: '/stella-qms/dashboard/customer-complaints' })
}

// 获取品牌翻箱率数据
export const getBrandUnboxingRate = () => {
  return request.get<BrandUnboxingRateVO[]>({ url: '/stella-qms/dashboard/brand-unboxing-rate' })
}

// 获取不良率偏高工单数据
export const getHighDefectWorkOrders = () => {
  return request.get<HighDefectWorkOrderVO[]>({ url: '/stella-qms/dashboard/high-defect-workorders' })
}

// 获取工厂列表数据
export const getFactoryList = () => {
  return request.get<string[]>({ url: '/stella-qms/dashboard/factory-list' })
}

// 获取QIP稽核数据
export const getQipAuditData = (dateStr?: string) => {
  return request.get<QipAuditVO[]>({ 
    url: '/stella-qms/dashboard/qip-audit-data',
    params: {
      dateStr
    }
  })
} 