import request from '@/config/axios'

export interface MailTemplateSubscriptionVO {
  id?: number
  userId: number
  userNickname?: string
  templateId: number
  templateCode: string
  templateName?: string
  subscriptionType: number
  status: number
  subscribeTime?: Date
  unsubscribeTime?: Date
  configs?: Record<string, string>
  createTime?: Date
}

export interface MailTemplateSubscriptionReqVO {
  templateCode: string
  subscriptionType?: number
  configs?: Record<string, string>
}

// 管理员分页查询参数
export interface MailTemplateSubscriptionPageReqVO extends PageParam {
  templateCode?: string
  status?: number
  userNickname?: string
}

// 订阅邮件模板
export const subscribeMailTemplate = async (data: MailTemplateSubscriptionReqVO) => {
  return await request.post({ url: '/system/mail-template-subscription/subscribe', data })
}

// 取消订阅邮件模板
export const unsubscribeMailTemplate = async (templateCode: string) => {
  return await request.delete({ 
    url: '/system/mail-template-subscription/unsubscribe?templateCode=' + templateCode 
  })
}

// 更新订阅状态
export const updateSubscriptionStatus = async (id: number, status: number) => {
  return await request.put({ 
    url: `/system/mail-template-subscription/update-status?id=${id}&status=${status}` 
  })
}

// 更新订阅配置
export const updateSubscriptionConfig = async (subscriptionId: number, configs: Record<string, string>) => {
  return await request.put({ 
    url: `/system/mail-template-subscription/update-config?subscriptionId=${subscriptionId}`,
    data: { configs }
  })
}

// 获得我的邮件模板订阅列表
export const getMyMailTemplateSubscriptionList = async () => {
  return await request.get({ url: '/system/mail-template-subscription/my-list' })
}

// 管理员分页查询邮件模板订阅列表
export const getMailTemplateSubscriptionPage = async (params: MailTemplateSubscriptionPageReqVO) => {
  return await request.get({ url: '/system/mail-template-subscription/page', params })
}

// 检查是否已订阅指定模板
export const checkSubscription = async (templateCode: string) => {
  return await request.get({ 
    url: '/system/mail-template-subscription/check-subscription?templateCode=' + templateCode 
  })
}

// 获得用户对指定模板的订阅信息
export const getSubscription = async (templateCode: string) => {
  return await request.get({ 
    url: '/system/mail-template-subscription/get-subscription?templateCode=' + templateCode 
  })
}

// 根据模板编码获得订阅列表（管理员使用）
export const getSubscriptionListByTemplate = async (templateCode: string) => {
  return await request.get({ 
    url: '/system/mail-template-subscription/list-by-template?templateCode=' + templateCode 
  })
}

// 获得指定模板的订阅统计（管理员使用）
export const getSubscriptionCountByTemplate = async (templateId: number) => {
  return await request.get({ 
    url: '/system/mail-template-subscription/count-by-template?templateId=' + templateId 
  })
} 