<template>
  <div class="ai-summary-container">
    <!-- AI总结悬浮按钮 -->
    <ElButton
      type="primary"
      circle
      size="large"
      :loading="isLoading"
      @click="handleAISummary"
      class="ai-summary-button"
      title="AI智能总结当前页面数据"
    >
      <template v-if="!isLoading">
        <Icon icon="ph:sparkle-bold" :size="32"/>
      </template>
    </ElButton>

    <!-- AI总结结果抽屉 -->
    <div v-if="summaryDialogVisible" class="ai-summary-drawer-container">
      <div class="ai-summary-drawer-overlay" @click="closeSummary"></div>
      <div class="ai-summary-drawer-content" :class="{ 'closing': isClosing }">
        <!-- 自定义标题栏 -->
        <div class="ai-summary-drawer-header">
          <div class="header-content">
            <div class="header-left">
              <div class="ai-avatar">
                <Icon icon="ph:robot-bold" class="avatar-icon" />
                <div class="avatar-glow"></div>
              </div>
              <div class="header-text">
                <h3 class="dialog-title">AI 智能数据分析</h3>
                <p class="dialog-subtitle">基于深度学习的数据洞察与总结</p>
              </div>
            </div>
            <div class="header-right">
              <ElButton
                type="text"
                @click="closeSummary"
                class="close-btn"
                circle
              >
                <Icon icon="ph:x-bold" />
              </ElButton>
            </div>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :class="{ 'completed': summaryResult }"></div>
          </div>
        </div>

        <div class="summary-content">
          <div v-if="summaryResult" class="summary-result">
            <div class="summary-header">
              <div class="analysis-badge">
                <Icon icon="ph:brain-bold" class="analysis-icon" />
                <span class="badge-text">分析完成</span>
                <div class="badge-glow"></div>
              </div>
              <div class="time-display-badge">
                <Icon icon="ph:clock-bold" class="time-icon" />
                <span class="time-text">{{ generateTime }}</span>
              </div>
            </div>
            
            <div class="summary-body">
              <MarkdownView :content="summaryResult" />
            </div>
            
            <div class="summary-footer">
              <div class="action-buttons">
                <ElButton
                  type="text"
                  @click="copySummary"
                  size="small"
                  class="action-btn copy-btn"
                >
                  <Icon icon="ph:copy-bold" />
                  <span>复制总结</span>
                  <div class="btn-glow"></div>
                </ElButton>
                <ElButton
                  type="text"
                  @click="generateAISummary"
                  :loading="isLoading"
                  size="small"
                  class="action-btn refresh-btn"
                >
                  <Icon icon="ph:arrow-clockwise-bold" />
                  <span>重新生成</span>
                  <div class="btn-glow"></div>
                </ElButton>
                <ElButton
                  type="text"
                  @click="clearSummary"
                  size="small"
                  class="action-btn clear-btn"
                >
                  <Icon icon="ph:trash-bold" />
                  <span>清除总结</span>
                  <div class="btn-glow"></div>
                </ElButton>
              </div>
            </div>
          </div>

          <div v-else-if="errorMessage" class="error-content">
            <div class="error-wrapper">
              <div class="error-icon">
                <Icon icon="ph:warning-circle-bold" />
                <div class="error-pulse"></div>
              </div>
              <h3 class="error-title">分析过程遇到问题</h3>
              <p class="error-message">{{ errorMessage }}</p>
              <ElButton type="primary" @click="generateAISummary" :loading="isLoading" class="retry-btn">
                <Icon icon="ph:arrow-clockwise-bold" />
                重新分析
              </ElButton>
            </div>
          </div>

          <div v-else class="loading-content">
            <div class="loading-wrapper">
              <div class="ai-thinking">
                <div class="thinking-dots">
                  <div class="dot"></div>
                  <div class="dot"></div>
                  <div class="dot"></div>
                </div>
                <Icon icon="ph:robot-bold" class="thinking-icon" />
              </div>
              <h3 class="loading-title">AI 正在深度分析数据</h3>
              <p class="loading-subtitle">运用先进AI大模型提取关键洞察...</p>
              <div class="loading-progress">
                <div class="progress-track">
                  <div class="progress-thumb"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <div class="footer-left">
            <div class="powered-by">
              <Icon icon="ph:lightning-bold" class="power-icon" />
              <span>Powered by Advanced AI</span>
            </div>
          </div>
          <div class="footer-right">
            <ElButton @click="closeSummary" class="footer-btn secondary">
              关闭
            </ElButton>
            <ElButton
              v-if="summaryResult"
              type="primary"
              @click="downloadSummary"
              class="footer-btn primary"
            >
              <Icon icon="ph:download-bold" />
              下载报告
            </ElButton>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
// 使用动态导入 MarkdownView 组件
const MarkdownView = defineAsyncComponent(() => import('@/components/MarkdownView/index.vue'))

interface AISummaryProps {
  /** 页面标识，用于区分不同页面的数据获取逻辑 */
  pageType?: string
  /** 自定义数据获取函数 */
  dataFetcher?: () => Promise<any>
  /** 额外的上下文信息 */
  context?: Record<string, any>
  /** 是否显示在右下角 */
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  /** 距离边缘的距离 */
  offset?: { x: number; y: number }
}

const props = withDefaults(defineProps<AISummaryProps>(), {
  pageType: 'dashboard',
  position: 'bottom-right',
  offset: () => ({ x: 30, y: 30 })
})

const emit = defineEmits<{
  summaryGenerated: [summary: string]
  summaryError: [error: string]
}>()

// 状态管理
const isLoading = ref(false)
const summaryDialogVisible = ref(false)
const isClosing = ref(false)
const summaryResult = ref('')
const errorMessage = ref('')
const generateTime = ref('')

// 计算属性
const buttonPosition = computed(() => {
  const { position, offset } = props
  const styles: Record<string, string> = {}
  
  if (position.includes('bottom')) {
    styles.bottom = `${offset.y}px`
  } else {
    styles.top = `${offset.y}px`
  }
  
  if (position.includes('right')) {
    styles.right = `${offset.x}px`
  } else {
    styles.left = `${offset.x}px`
  }
  
  return styles
})

// 主要功能函数
const handleAISummary = async () => {
  // 如果已经有总结结果，直接显示抽屉
  if (summaryResult.value) {
    summaryDialogVisible.value = true
    return
  }
  
  // 如果没有结果，则生成AI总结
  await generateAISummary()
}

// 关闭抽屉
const closeSummary = () => {
  // 设置关闭状态，触发关闭动画
  isClosing.value = true
  
  // 等待动画完成后再关闭抽屉
  setTimeout(() => {
    summaryDialogVisible.value = false
    // 重置关闭状态
    setTimeout(() => {
      isClosing.value = false
    }, 100)
  }, 300) // 与CSS动画时间匹配
}

// 生成AI总结的函数
const generateAISummary = async () => {
  try {
    isLoading.value = true
    errorMessage.value = ''
    summaryDialogVisible.value = true
    
    // 直接调用父组件传入的数据获取函数
    if (props.dataFetcher) {
      const result = await props.dataFetcher()
      
      // 期望父组件返回的是AI分析结果（字符串）
      if (typeof result === 'string') {
        summaryResult.value = result
      } else {
        throw new Error('数据获取函数应该返回AI分析结果字符串')
      }
    } else {
      throw new Error('没有提供数据获取函数')
    }
    
    generateTime.value = new Date().toLocaleString('zh-CN')
    emit('summaryGenerated', summaryResult.value)
    ElMessage.success('AI总结生成成功')
    
  } catch (error) {
    console.error('AI总结失败:', error)
    const errorMsg = error instanceof Error ? error.message : '未知错误'
    errorMessage.value = errorMsg
    emit('summaryError', errorMsg)
    ElMessage.error('AI总结生成失败')
  } finally {
    isLoading.value = false
  }
}

// 工具函数
const copySummary = async () => {
  try {
    await navigator.clipboard.writeText(summaryResult.value)
    ElMessage.success('总结已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败')
  }
}

const clearSummary = () => {
  summaryResult.value = ''
  errorMessage.value = ''
  generateTime.value = ''
  ElMessage.success('总结已清除')
}

const downloadSummary = () => {
  try {
    const blob = new Blob([summaryResult.value], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `AI总结_${generateTime.value.replace(/[/:]/g, '-')}.md`
    link.click()
    URL.revokeObjectURL(url)
    ElMessage.success('总结已下载')
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载失败')
  }
}
</script>

<style lang="scss" scoped>
.ai-summary-container {
  .ai-summary-button {
    position: fixed;
    bottom: v-bind('buttonPosition.bottom');
    right: v-bind('buttonPosition.right');
    top: v-bind('buttonPosition.top');
    left: v-bind('buttonPosition.left');
    z-index: 1000;
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border: none;
    box-shadow: 
      0 8px 32px rgba(59, 130, 246, 0.4),
      0 0 20px rgba(29, 78, 216, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    
    &:hover {
      transform: scale(1.1) translateY(-2px);
      box-shadow: 
        0 12px 40px rgba(59, 130, 246, 0.6),
        0 0 30px rgba(29, 78, 216, 0.5);
    }
    
    .iconify {
      font-size: 24px;
      color: white;
      filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
    }
  }
}

// 抽屉容器样式
.ai-summary-drawer-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2000;
}

.ai-summary-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2001;
  animation: fadeIn 0.3s ease forwards;
}

.ai-summary-drawer-content {
  position: fixed;
  top: 0;
  right: 0;
  width: 80%;
  height: 100%;
  background: linear-gradient(135deg, 
    rgba(248, 250, 252, 0.98) 0%, 
    rgba(241, 245, 249, 0.98) 50%, 
    rgba(248, 250, 252, 0.98) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  backdrop-filter: blur(20px);
  box-shadow: 
    -25px 0 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
  z-index: 2002;
  animation: slideIn 0.3s ease forwards;

  &.closing {
    animation: slideOut 0.3s ease forwards;
  }
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(29, 78, 216, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(96, 165, 250, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideOut {
  from { transform: translateX(0); }
  to { transform: translateX(100%); }
}

.ai-summary-drawer-header {
  padding: 24px 24px 0;
  background: linear-gradient(90deg, 
    rgba(59, 130, 246, 0.08) 0%, 
    rgba(96, 165, 250, 0.08) 100%);
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .ai-avatar {
        position: relative;
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        .avatar-icon {
          font-size: 24px;
          color: white;
          z-index: 2;
        }
        
        .avatar-glow {
          position: absolute;
          top: -2px;
          left: -2px;
          right: -2px;
          bottom: -2px;
          background: linear-gradient(135deg, #3b82f6, #1d4ed8);
          border-radius: 14px;
          z-index: 1;
          opacity: 0.6;
          animation: pulse-glow 2s ease-in-out infinite alternate;
        }
      }
      
      .header-text {
        .dialog-title {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: #1e293b;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
        
        .dialog-subtitle {
          margin: 4px 0 0;
          font-size: 14px;
          color: #64748b;
        }
      }
    }
    
    .header-right {
      .close-btn {
        width: 32px;
        height: 32px;
        background: rgba(148, 163, 184, 0.1);
        border: 1px solid rgba(148, 163, 184, 0.2);
        border-radius: 8px;
        color: #64748b;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(239, 68, 68, 0.1);
          border-color: rgba(239, 68, 68, 0.3);
          color: #ef4444;
          transform: scale(1.1);
        }
      }
    }
  }
  
  .progress-bar {
    height: 2px;
    background: rgba(148, 163, 184, 0.2);
    border-radius: 1px;
    overflow: hidden;
    
    .progress-fill {
      height: 100%;
      width: 0%;
      background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
      transition: width 0.8s ease;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, 
          transparent 0%, 
          rgba(255, 255, 255, 0.4) 50%, 
          transparent 100%);
        animation: shimmer 2s infinite;
      }
      
      &.completed {
        width: 100%;
      }
    }
  }
}

.summary-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(148, 163, 184, 0.1);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 3px;
  }
}

.summary-result {
  .summary-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    
    .analysis-badge {
      position: relative;
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: rgba(34, 197, 94, 0.1);
      border: 1px solid rgba(34, 197, 94, 0.3);
      border-radius: 20px;
      
      .analysis-icon {
        font-size: 18px;
        color: #16a34a;
        animation: pulse-icon 2s ease-in-out infinite;
      }
      
      .badge-text {
        font-size: 14px;
        font-weight: 500;
        color: #15803d;
      }
      
      .badge-glow {
        position: absolute;
        top: -1px;
        left: -1px;
        right: -1px;
        bottom: -1px;
        background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), transparent);
        border-radius: 20px;
        z-index: -1;
        animation: badge-glow 3s ease-in-out infinite;
      }
    }
    
    .time-display-badge {
      position: relative;
      display: flex;
      align-items: center;
      gap: 4px; 
      padding: 6px 10px;
      background: rgba(59, 130, 246, 0.1);
      border: 1px solid rgba(59, 130, 246, 0.3);
      border-radius: 16px; 
      
      .time-icon {
        font-size: 14px; 
        color: #2563eb; 
      }
      
      .time-text {
        font-size: 12px;
        font-weight: 500;
        color: #2563eb; 
      }
    }
  }
  
  .summary-body {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    padding: 24px;
    line-height: 1.7;
    color: #334155;
    position: relative;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(59, 130, 246, 0.5) 50%, 
        transparent 100%);
    }
    
    :deep(h1), :deep(h2), :deep(h3) {
      color: #0f172a;
      margin: 20px 0 12px 0;
      font-weight: 600;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      
      .emoji {
        display: inline-block;
        margin-right: 8px;
        font-style: normal;
        font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', sans-serif;
        font-size: 1.2em;
        vertical-align: middle;
        filter: none;
        color: initial;
        -webkit-text-fill-color: initial;
        background: none;
        -webkit-background-clip: initial;
        background-clip: initial;
      }
    }
    
    :deep(strong) {
      color: #1e40af;
      font-weight: 600;
    }
    
    :deep(em) {
      color: #d97706;
      font-style: italic;
    }
    
    :deep(ul), :deep(ol) {
      padding-left: 24px;
      
      li {
        margin: 8px 0;
        position: relative;
        color: #475569;
        
        &::marker {
          color: #3b82f6;
        }
      }
    }
    
    :deep(code) {
      background: rgba(59, 130, 246, 0.1);
      color: #1e40af;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'JetBrains Mono', 'Fira Code', monospace;
      border: 1px solid rgba(59, 130, 246, 0.2);
    }
    
    :deep(pre) {
      background: rgba(248, 250, 252, 0.9);
      padding: 16px;
      border-radius: 8px;
      overflow-x: auto;
      border: 1px solid rgba(59, 130, 246, 0.2);
      
      code {
        background: none;
        border: none;
        padding: 0;
        color: #1e293b;
      }
    }
  }
  
  .summary-footer {
    .action-buttons {
      display: flex;
      gap: 12px;
      
      .action-btn {
        position: relative;
        display: flex;
        align-items: center;
        gap: 6px;
        padding: 8px 16px;
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(59, 130, 246, 0.3);
        border-radius: 8px;
        color: #1e40af;
        transition: all 0.3s ease;
        overflow: hidden;
        
        &:hover {
          background: rgba(59, 130, 246, 0.1);
          border-color: rgba(59, 130, 246, 0.5);
          color: #1d4ed8;
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }
        
        .btn-glow {
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, 
            transparent 0%, 
            rgba(59, 130, 246, 0.2) 50%, 
            transparent 100%);
          transition: left 0.6s ease;
        }
        
        &:hover .btn-glow {
          left: 100%;
        }
        
        &.clear-btn {
          border-color: rgba(239, 68, 68, 0.3);
          color: #dc2626;
          
          &:hover {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.5);
            color: #b91c1c;
          }
        }
        
        .iconify {
          font-size: 16px;
          z-index: 2;
        }
        
        span {
          z-index: 2;
        }
      }
    }
  }
}

.error-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  
  .error-wrapper {
    text-align: center;
    
    .error-icon {
      position: relative;
      display: inline-block;
      margin-bottom: 16px;
      
      .iconify {
        font-size: 48px;
        color: #ef4444;
      }
      
      .error-pulse {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80px;
        height: 80px;
        border: 2px solid rgba(239, 68, 68, 0.3);
        border-radius: 50%;
        animation: pulse-ring 2s ease-out infinite;
      }
    }
    
    .error-title {
      margin: 0 0 12px;
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
    }
    
    .error-message {
      margin: 0 0 24px;
      color: #64748b;
      font-size: 14px;
    }
    
    .retry-btn {
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
      border: none;
      color: white;
      padding: 10px 24px;
      border-radius: 8px;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
      }
    }
  }
}

.loading-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 350px;
  
  .loading-wrapper {
    text-align: center;
    
    .ai-thinking {
      position: relative;
      display: inline-block;
      margin-bottom: 32px;
      
      .thinking-dots {
        display: flex;
        gap: 8px;
        justify-content: center;
        margin-bottom: 16px;
        
        .dot {
          width: 8px;
          height: 8px;
          background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
          border-radius: 50%;
          animation: thinking-bounce 1.4s ease-in-out infinite both;
          
          &:nth-child(1) { animation-delay: -0.32s; }
          &:nth-child(2) { animation-delay: -0.16s; }
          &:nth-child(3) { animation-delay: 0s; }
        }
      }
      
      .thinking-icon {
        font-size: 40px;
        color: #3b82f6;
        animation: float 3s ease-in-out infinite;
      }
    }
    
    .loading-title {
      margin: 0 0 8px;
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
      background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .loading-subtitle {
      margin: 0 0 24px;
      color: #64748b;
      font-size: 14px;
    }
    
    .loading-progress {
      .progress-track {
        width: 200px;
        height: 4px;
        background: rgba(148, 163, 184, 0.2);
        border-radius: 2px;
        overflow: hidden;
        margin: 0 auto;
        position: relative;
        
        .progress-thumb {
          height: 100%;
          width: 60px;
          background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
          border-radius: 2px;
          animation: loading-slide 2s ease-in-out infinite;
          
          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, 
              transparent 0%, 
              rgba(255, 255, 255, 0.4) 50%, 
              transparent 100%);
            animation: shimmer 1.5s infinite;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 24px;
  background: linear-gradient(90deg, 
    rgba(248, 250, 252, 0.9) 0%, 
    rgba(241, 245, 249, 0.9) 100%);
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  
  .footer-left {
    .powered-by {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #64748b;
      font-size: 12px;
      
      .power-icon {
        font-size: 14px;
        color: #3b82f6;
        animation: pulse-icon 2s ease-in-out infinite;
      }
    }
  }
  
  .footer-right {
    display: flex;
    gap: 12px;
    
    .footer-btn {
      padding: 8px 20px;
      border-radius: 8px;
      transition: all 0.3s ease;
      
      &.secondary {
        background: rgba(148, 163, 184, 0.1);
        border: 1px solid rgba(148, 163, 184, 0.3);
        color: #475569;
        
        &:hover {
          background: rgba(148, 163, 184, 0.2);
          transform: translateY(-1px);
        }
      }
      
      &.primary {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        border: none;
        color: white;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }
        
        .iconify {
          margin-right: 6px;
        }
      }
    }
  }
}

// 动画定义
@keyframes pulse-glow {
  0% { opacity: 0.6; transform: scale(1); }
  100% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes pulse-icon {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes badge-glow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

@keyframes pulse-ring {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(2);
    opacity: 0;
  }
}

@keyframes thinking-bounce {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes loading-slide {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(250%); }
  100% { transform: translateX(-100%); }
}
</style> 