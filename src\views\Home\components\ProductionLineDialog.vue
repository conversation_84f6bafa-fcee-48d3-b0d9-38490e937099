<script setup lang="ts">
import { ref, computed, onUnmounted, nextTick, watch } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { useResizeObserver } from '@vueuse/core';

// Register ECharts components
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  BarChart,
  CanvasRenderer,
]);

// 定义生产线数据接口
interface ProductionLineData {
  lineNo: string;
  defectRate: number;
}

// 定义组件属性
interface Props {
  visible: boolean;
  factNo: string;
  factName: string;
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  'update:visible': [value: boolean];
}>();

// 响应式数据
const chartData = ref<ProductionLineData[]>([]);
const chartRef = ref<HTMLElement | null>(null);
const loading = ref(false);
let chartInstance: echarts.ECharts | null = null;

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

// 图表配置
const chartOption = computed(() => ({
  backgroundColor: 'transparent',
  title: {
    text: `${props.factName} - 各线别理鞋不良率`,
    left: 'center',
    textStyle: {
      color: '#303133',
      fontSize: 16,
      fontWeight: 'normal'
    },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: { 
      type: 'shadow', 
      shadowStyle: { color: 'rgba(150, 150, 150, 0.1)' } 
    },
    backgroundColor: '#FFFFFF',
    borderColor: '#DCDFE6',
    borderWidth: 1,
    textStyle: { 
      color: '#303133',
      fontSize: 12
    },
    padding: [8, 12],
    formatter: (params: any) => {
      const param = Array.isArray(params) ? params[0] : params;
      if (param && typeof param.name !== 'undefined' && typeof param.value !== 'undefined') {
        return `${param.name}: ${param.value}%`;
      }
      return '';
    }
  },
  grid: { 
    top: '80', 
    right: '30', 
    bottom: '60', 
    left: '60' 
  },
  xAxis: {
    type: 'category',
    data: chartData.value.map(item => item.lineNo),
    axisLine: { lineStyle: { color: '#DCDFE6' } },
    splitLine: { show: false },
    axisTick: { show: false },
    axisLabel: { 
      color: '#909399',
      fontSize: 12,
      rotate: 0
    },
  },
  yAxis: {
    type: 'value',
    axisLine: { lineStyle: { color: '#DCDFE6' } },
    splitLine: { 
      lineStyle: { 
        color: '#EBEEF5', 
        type: 'dashed', 
        opacity: 0.8 
      } 
    },
    axisTick: { show: false },
    axisLabel: { 
      color: '#909399',
      fontSize: 12,
      formatter: '{value}%'
    },
  },
  series: [{
    name: '理鞋不良率',
    type: 'bar',
    data: chartData.value.map(item => item.defectRate),
    itemStyle: { 
      color: '#F39C12',
      borderRadius: [4, 4, 0, 0] 
    },
    barWidth: '60%',
    emphasis: { 
      itemStyle: { 
        color: '#E67E22',
        opacity: 0.8
      } 
    }
  }],
}));

// 获取生产线数据
const fetchProductionLineData = async () => {
  try {
    loading.value = true;
    // TODO: 调用实际API
    // const res = await getProductionLineData(props.factNo);
    // chartData.value = res;
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 600));
    
    const mockData: Record<string, ProductionLineData[]> = {
      'AL': [
        { lineNo: 'Line-01', defectRate: 3.2 },
        { lineNo: 'Line-02', defectRate: 2.8 },
        { lineNo: 'Line-03', defectRate: 2.1 },
        { lineNo: 'Line-04', defectRate: 3.5 },
        { lineNo: 'Line-05', defectRate: 2.9 },
        { lineNo: 'Line-06', defectRate: 2.4 }
      ],
      'TP': [
        { lineNo: 'Line-A', defectRate: 2.1 },
        { lineNo: 'Line-B', defectRate: 1.8 },
        { lineNo: 'Line-C', defectRate: 1.5 },
        { lineNo: 'Line-D', defectRate: 2.3 },
        { lineNo: 'Line-E', defectRate: 1.9 }
      ],
      'GN': [
        { lineNo: 'L1', defectRate: 2.5 },
        { lineNo: 'L2', defectRate: 2.0 },
        { lineNo: 'L3', defectRate: 1.8 },
        { lineNo: 'L4', defectRate: 2.2 },
        { lineNo: 'L5', defectRate: 2.1 },
        { lineNo: 'L6', defectRate: 1.9 }
      ],
      'SS': [
        { lineNo: 'P1', defectRate: 1.8 },
        { lineNo: 'P2', defectRate: 1.2 },
        { lineNo: 'P3', defectRate: 1.5 },
        { lineNo: 'P4', defectRate: 1.3 }
      ],
      'SF': [
        { lineNo: 'A线', defectRate: 1.5 },
        { lineNo: 'B线', defectRate: 1.0 },
        { lineNo: 'C线', defectRate: 1.2 },
        { lineNo: 'D线', defectRate: 1.1 }
      ],
      'HN': [
        { lineNo: '1号线', defectRate: 2.8 },
        { lineNo: '2号线', defectRate: 2.1 },
        { lineNo: '3号线', defectRate: 2.5 },
        { lineNo: '4号线', defectRate: 2.0 },
        { lineNo: '5号线', defectRate: 2.3 }
      ],
      'BJ': [
        { lineNo: 'Line1', defectRate: 2.0 },
        { lineNo: 'Line2', defectRate: 1.6 },
        { lineNo: 'Line3', defectRate: 1.8 },
        { lineNo: 'Line4', defectRate: 1.9 }
      ],
      'SZ': [
        { lineNo: 'A', defectRate: 2.2 },
        { lineNo: 'B', defectRate: 1.9 },
        { lineNo: 'C', defectRate: 2.1 },
        { lineNo: 'D', defectRate: 1.8 },
        { lineNo: 'E', defectRate: 2.0 }
      ]
    };
    
    chartData.value = mockData[props.factNo] || [];
  } catch (error) {
    console.error('获取生产线数据失败:', error);
    chartData.value = [];
  } finally {
    loading.value = false;
  }
};

// 初始化图表
const initChart = () => {
  if (chartRef.value && !chartInstance) {
    chartInstance = echarts.init(chartRef.value);
    chartInstance.setOption(chartOption.value);
  }
};

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(chartOption.value, { notMerge: true });
  } else {
    nextTick(initChart);
  }
};

// 调整图表大小
const resizeChart = () => {
  chartInstance?.resize();
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.factNo) {
    fetchProductionLineData();
    nextTick(() => {
      initChart();
      if (chartRef.value) {
        useResizeObserver(chartRef.value, resizeChart);
      }
    });
  }
});

// 监听图表数据变化
watch(chartData, () => {
  updateChart();
}, { deep: true });

// 组件卸载时清理
onUnmounted(() => {
  chartInstance?.dispose();
  chartInstance = null;
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="`${factName} - 各线别理鞋不良率分析`"
    width="800px"
    class="production-line-dialog"
    destroy-on-close
    @close="chartInstance?.dispose(); chartInstance = null;"
  >
    <div class="dialog-content" v-loading="loading" element-loading-text="正在加载数据...">
      <div class="factory-info">
        <el-tag type="primary" size="large">{{ factName }} ({{ factNo }})</el-tag>
        <span class="info-text">各生产线理鞋不良率详情</span>
      </div>
      
      <div class="chart-container">
        <div ref="chartRef" class="chart-wrapper"></div>
      </div>
      
      <div class="data-summary" v-if="chartData.length > 0">
        <div class="summary-item">
          <span class="label">最高不良率:</span>
          <span class="value high">{{ Math.max(...chartData.map(item => item.defectRate)) }}%</span>
        </div>
        <div class="summary-item">
          <span class="label">最低不良率:</span>
          <span class="value low">{{ Math.min(...chartData.map(item => item.defectRate)) }}%</span>
        </div>
        <div class="summary-item">
          <span class="label">平均不良率:</span>
          <span class="value avg">{{ (chartData.reduce((sum, item) => sum + item.defectRate, 0) / chartData.length).toFixed(2) }}%</span>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
.production-line-dialog {
  .el-dialog__header {
    background-color: #f39c12;
    border-bottom: 1px solid #ebeef5;
    
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: white;
    }
    
    .el-dialog__headerbtn .el-icon {
      color: white;
    }
  }
}

.dialog-content {
  min-height: 450px;
}

.factory-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #f39c12;
  
  .info-text {
    color: #606266;
    font-size: 14px;
  }
}

.chart-container {
  width: 100%;
  height: 350px;
  margin-bottom: 20px;
}

.chart-wrapper {
  width: 100%;
  height: 100%;
}

.data-summary {
  display: flex;
  justify-content: space-around;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #ebeef5;
  
  .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    
    .label {
      font-size: 12px;
      color: #909399;
    }
    
    .value {
      font-size: 16px;
      font-weight: bold;
      
      &.high {
        color: #e74c3c;
      }
      
      &.low {
        color: #27ae60;
      }
      
      &.avg {
        color: #f39c12;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .data-summary {
    flex-direction: column;
    gap: 10px;
    
    .summary-item {
      flex-direction: row;
      justify-content: space-between;
    }
  }
}
</style>
