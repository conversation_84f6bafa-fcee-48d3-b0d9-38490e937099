import request from '@/config/axios'

// 定义返回类型接口
export interface PageResult<T> {
  list: T[]
  total: number
}

export interface FinalInspectionDetailVO {
  v_id: string
  select_date: string
  brand_name: string
  model_no: string
  color_name: string
  team_jc: string
  qc_name: string
  final_type: number
  inpa_num: number
  bl_num: number
  bl_rate: number
  ord_qty: number
  order_no: string
  fqc_result: string
  fact_no: string
  pic_addr0?: string
  pic_addr1?: string
  pic_addr2?: string
  pic_addr3?: string
  [key: string]: any // 用于处理动态属性 pj_c1, pj_c2 等
}

export interface FinalInspectionStatisticsVO {
  factNo: string
  brandName?: string
  finalType?: number
  sampleQty?: number
  ordQty?: number
  rating?: number
  singleSampleQty?: number
  singleQty?: number
  singleRating?: number
}

export interface BrandDefectRateVO {
  brandName: string
  finalType?: number
  totalInspected: number
  defectCount: number
  defectRate: number
  totalCount: number
  hqCount: number
  brandAuditCount: number
  thirdCount: number
  factoryCount: number
}

export interface FinalInspectionPageReqVO {
  pageNo: number
  pageSize: number
  factNo?: string
  brandName?: string
  finalType?: string
  dateRange?: string[]
}

export interface PjDefectRespVO {
  id: number
  name: string
  ename: string
  vnname: string
}

export interface DefectRateStatisticsVO {
  totalInspectionCount: number
  totalInspectionPairs: number
  totalDefectPairs: number
  totalSampledPairs: number
  averageDefectRate: number
  growthRate?: number
  month?: string
}

export interface InspectionReportVO {
  vId: number
  finalType: string
  basicInfo: {
    factNo: string
    brandName: string
    modelNo: string
    skuNo: string
    colorName: string
    teamId: number
    teamJc: string
    qcId: number
    qcName: string
    dutyPerson: string
    selectDate: string
    custPo: string
    ordQty: number
    inpaNum: number
    picAddr: string
  }
  defectList: Array<{
    defectKind: number
    defectName: string
    criticalDefect: number
    majorDefect: number
    minorDefect: number
  }>
  criticalDefectCount: number
  majorDefectCount: number
  minorDefectCount: number
  totalDefectCount: number
  criticalPictures: string[]
  majorPictures: string[]
  minorPictures: string[]
}

// 获取最终验货分析明细分页
export const getFinalInspectionPage = (params: FinalInspectionPageReqVO) => {
  return request.get<PageResult<FinalInspectionDetailVO>>({
    url: '/stella-qms/final-inspection/page',
    params
  })
}

// 获取最终验货统计数据
export const getFinalInspectionStatistics = (factNo?: string, brandName?: string, finalType?: number, month?: string) => {
  return request.get<FinalInspectionStatisticsVO[]>({
    url: '/stella-qms/final-inspection/statistics',
    params: {
      factNo,
      brandName,
      finalType,
      month
    }
  })
}

// 获取品牌不良率统计数据
export const getBrandDefectRate = (factNo?: string, finalType?: number, month?: string) => {
  return request.get<BrandDefectRateVO[]>({
    url: '/stella-qms/final-inspection/brand-defect-rate',
    params: {
      factNo,
      finalType,
      month
    }
  })
}

// 获取问题点列表
export const getDefectList = () => {
  return request.get<PjDefectRespVO[]>({
    url: '/stella-qms/final-inspection/defects'
  })
}

// 获取工厂列表
export const getFactoryList = () => {
  return request.get<string[]>({
    url: '/stella-qms/final-inspection/factory-list'
  })
}

// 获取品牌列表
export const getBrandList = () => {
  return request.get<string[]>({
    url: '/stella-qms/final-inspection/brand-list'
  })
}

// 导出最终验货分析明细数据
export const exportFinalInspectionData = (params: FinalInspectionPageReqVO) => {
  return request.get<Boolean>({
    url: '/stella-qms/final-inspection/export',
    params
  })
}

// 获取不良率统计数据
export const getDefectRateStatistics = (factNo?: string, brandName?: string, finalType?: number, month?: string) => {
  return request.get<DefectRateStatisticsVO>({
    url: '/stella-qms/final-inspection/defect-rate-statistics',
    params: {
      factNo,
      brandName,
      finalType,
      month
    }
  })
}

// 获取验货报告详细信息
export const getInspectionReport = (vId: number, date: string, factory: string, finalType: string, 
                                    brandName: string, modelNo: string, teamId: String, skuNo?: string) => {
  return request.get<InspectionReportVO>({
    url: '/stella-qms/final-inspection/report',
    params: {
      vId,
      date,
      factory,
      finalType,
      brandName,
      modelNo,
      teamId,
      skuNo
    }
  })
} 