<script setup lang="ts">
import { ref, computed, onMounted, nextTick, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { useResizeObserver } from '@vueuse/core';

// Register ECharts components
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  BarChart,
  CanvasRenderer,
]);

// 定义质量指标数据接口
interface QualityMetricsData {
  shoeDefectRate: number;      // 理鞋不良率
  unboxingRate: number;        // 翻箱率
  qualityComplaints: number;   // 品质抱怨案件
  complaintAmount: number;     // 客诉金额
}

// 定义工厂数据接口
interface FactoryMetricData {
  factNo: string;
  value: number;
}

// 定义指标类型
type MetricType = 'shoeDefectRate' | 'unboxingRate' | 'qualityComplaints' | 'complaintAmount';

const loading = ref(true);
const chartLoading = ref(false);
const activeMetric = ref<MetricType>('shoeDefectRate'); // 默认选中理鞋不良率
const metricsData = ref<QualityMetricsData>({
  shoeDefectRate: 0,
  unboxingRate: 0,
  qualityComplaints: 0,
  complaintAmount: 0
});

const chartData = ref<FactoryMetricData[]>([]);
const chartRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

// 事件定义
const emit = defineEmits<{
  openProductionLine: [factNo: string, factName: string]
}>();

// 指标配置
const metricConfigs = {
  shoeDefectRate: { title: '理鞋不良率', color: '#F39C12', unit: '%' },
  unboxingRate: { title: '翻箱率', color: '#8E44AD', unit: '%' },
  qualityComplaints: { title: '品质抱怨案件', color: '#3498DB', unit: '' },
  complaintAmount: { title: '客诉金额', color: '#2ECC71', unit: '元' }
};

// 获取质量指标数据
const fetchData = async () => {
  try {
    loading.value = true;
    // TODO: 调用实际API
    // const res = await getQualityMetrics();
    // metricsData.value = res;
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000));
    metricsData.value = {
      shoeDefectRate: 2.35,
      unboxingRate: 15.8,
      qualityComplaints: 23,
      complaintAmount: 45600
    };
  } catch (error) {
    console.error('获取质量指标数据失败:', error);
  } finally {
    loading.value = false;
  }
};

// 获取工厂数据
const fetchFactoryData = async (metricType: MetricType) => {
  try {
    chartLoading.value = true;
    // TODO: 调用实际API
    // const res = await getFactoryMetricData(metricType);
    // chartData.value = res;
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockData: Record<MetricType, FactoryMetricData[]> = {
      shoeDefectRate: [
        { factNo: 'AL', value: 2.8 },
        { factNo: 'TP', value: 1.9 },
        { factNo: 'GN', value: 2.1 },
        { factNo: 'SS', value: 1.5 },
        { factNo: 'SF', value: 1.2 },
        { factNo: 'HN', value: 2.3 },
        { factNo: 'BJ', value: 1.8 },
        { factNo: 'SZ', value: 2.0 }
      ],
      unboxingRate: [
        { factNo: 'AL', value: 18.5 },
        { factNo: 'TP', value: 12.3 },
        { factNo: 'GN', value: 15.7 },
        { factNo: 'SS', value: 9.8 },
        { factNo: 'SF', value: 11.2 },
        { factNo: 'HN', value: 16.4 },
        { factNo: 'BJ', value: 13.9 },
        { factNo: 'SZ', value: 14.6 }
      ],
      qualityComplaints: [
        { factNo: 'AL', value: 8 },
        { factNo: 'TP', value: 5 },
        { factNo: 'GN', value: 6 },
        { factNo: 'SS', value: 3 },
        { factNo: 'SF', value: 4 },
        { factNo: 'HN', value: 7 },
        { factNo: 'BJ', value: 5 },
        { factNo: 'SZ', value: 6 }
      ],
      complaintAmount: [
        { factNo: 'AL', value: 15600 },
        { factNo: 'TP', value: 8900 },
        { factNo: 'GN', value: 12300 },
        { factNo: 'SS', value: 5600 },
        { factNo: 'SF', value: 7800 },
        { factNo: 'HN', value: 13400 },
        { factNo: 'BJ', value: 9200 },
        { factNo: 'SZ', value: 10500 }
      ]
    };
    
    chartData.value = mockData[metricType] || [];
  } catch (error) {
    console.error('获取工厂数据失败:', error);
    chartData.value = [];
  } finally {
    chartLoading.value = false;
  }
};

// 处理指标点击事件
const handleMetricClick = (metricType: MetricType) => {
  activeMetric.value = metricType;
  fetchFactoryData(metricType);
};

// 格式化数值显示
const formatValue = (value: number, type: MetricType): string => {
  switch (type) {
    case 'shoeDefectRate':
    case 'unboxingRate':
      return `${value}%`;
    case 'complaintAmount':
      return `¥${value.toLocaleString()}`;
    default:
      return value.toString();
  }
};

// 图表配置
const chartOption = computed(() => {
  const config = metricConfigs[activeMetric.value];
  return {
    backgroundColor: 'transparent',
    title: {
      text: `${config.title} - 工厂对比`,
      left: 'center',
      textStyle: {
        color: '#303133',
        fontSize: 14,
        fontWeight: 'normal'
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: { 
        type: 'shadow', 
        shadowStyle: { color: 'rgba(150, 150, 150, 0.1)' } 
      },
      backgroundColor: '#FFFFFF',
      borderColor: '#DCDFE6',
      borderWidth: 1,
      textStyle: { 
        color: '#303133',
        fontSize: 12
      },
      padding: [8, 12],
      formatter: (params: any) => {
        const param = Array.isArray(params) ? params[0] : params;
        if (param && typeof param.name !== 'undefined' && typeof param.value !== 'undefined') {
          return `${param.name}: ${formatValue(param.value, activeMetric.value)}`;
        }
        return '';
      }
    },
    grid: { 
      top: '50', 
      right: '20', 
      bottom: '40', 
      left: '50' 
    },
    xAxis: {
      type: 'category',
      data: chartData.value.map(item => item.factNo),
      axisLine: { lineStyle: { color: '#DCDFE6' } },
      splitLine: { show: false },
      axisTick: { show: false },
      axisLabel: { 
        color: '#909399',
        fontSize: 11,
        rotate: 0
      },
    },
    yAxis: {
      type: 'value',
      axisLine: { lineStyle: { color: '#DCDFE6' } },
      splitLine: { 
        lineStyle: { 
          color: '#EBEEF5', 
          type: 'dashed', 
          opacity: 0.8 
        } 
      },
      axisTick: { show: false },
      axisLabel: { 
        color: '#909399',
        fontSize: 11,
        formatter: `{value}${config.unit}`
      },
    },
    series: [{
      name: config.title,
      type: 'bar',
      data: chartData.value.map(item => item.value),
      itemStyle: { 
        color: config.color,
        borderRadius: [3, 3, 0, 0] 
      },
      barWidth: '60%',
      emphasis: { 
        itemStyle: { 
          color: config.color,
          opacity: 0.8
        } 
      }
    }],
  };
});

// 初始化图表
const initChart = () => {
  if (chartRef.value && !chartInstance) {
    chartInstance = echarts.init(chartRef.value);
    chartInstance.setOption(chartOption.value);
    
    // 添加点击事件（仅对理鞋不良率有效）
    chartInstance.on('click', (params: any) => {
      if (params.componentType === 'series' && activeMetric.value === 'shoeDefectRate') {
        const factNo = params.name;
        emit('openProductionLine', factNo, `${factNo}工厂`);
      }
    });
  }
};

// 更新图表
const updateChart = () => {
  if (chartInstance) {
    chartInstance.setOption(chartOption.value, { notMerge: true });
  } else {
    nextTick(initChart);
  }
};

// 调整图表大小
const resizeChart = () => {
  chartInstance?.resize();
};

// 监听图表数据变化
watch(chartData, () => {
  updateChart();
}, { deep: true });

// 监听活跃指标变化
watch(activeMetric, () => {
  updateChart();
});

onMounted(async () => {
  await fetchData();
  await fetchFactoryData(activeMetric.value);
  nextTick(() => {
    initChart();
    if (chartRef.value) {
      useResizeObserver(chartRef.value, resizeChart);
    }
  });
});

// 组件卸载时清理
onUnmounted(() => {
  chartInstance?.dispose();
  chartInstance = null;
});
</script>

<template>
  <div class="quality-metrics-card">
    <h3 class="card-title">质量关键指标</h3>
    <el-skeleton :loading="loading" animated>
      <template #default>
        <div class="metrics-grid">
          <div 
            class="metric-item metric-orange"
            :class="{ active: activeMetric === 'shoeDefectRate' }"
            @click="handleMetricClick('shoeDefectRate')"
          >
            <div class="metric-title">理鞋不良率</div>
            <div class="metric-value">{{ formatValue(metricsData.shoeDefectRate, 'shoeDefectRate') }}</div>
            <div class="metric-subtitle">点击查看工厂对比</div>
          </div>
          
          <div 
            class="metric-item metric-purple"
            :class="{ active: activeMetric === 'unboxingRate' }"
            @click="handleMetricClick('unboxingRate')"
          >
            <div class="metric-title">翻箱率</div>
            <div class="metric-value">{{ formatValue(metricsData.unboxingRate, 'unboxingRate') }}</div>
            <div class="metric-subtitle">点击查看工厂对比</div>
          </div>
          
          <div 
            class="metric-item metric-blue"
            :class="{ active: activeMetric === 'qualityComplaints' }"
            @click="handleMetricClick('qualityComplaints')"
          >
            <div class="metric-title">品质抱怨案件</div>
            <div class="metric-value">{{ formatValue(metricsData.qualityComplaints, 'qualityComplaints') }}</div>
            <div class="metric-subtitle">点击查看工厂对比</div>
          </div>
          
          <div 
            class="metric-item metric-green"
            :class="{ active: activeMetric === 'complaintAmount' }"
            @click="handleMetricClick('complaintAmount')"
          >
            <div class="metric-title">客诉金额</div>
            <div class="metric-value">{{ formatValue(metricsData.complaintAmount, 'complaintAmount') }}</div>
            <div class="metric-subtitle">点击查看工厂对比</div>
          </div>
        </div>
        
        <!-- 工厂对比图表 -->
        <div class="chart-section" v-loading="chartLoading" element-loading-text="正在加载图表...">
          <div ref="chartRef" class="chart-container"></div>
          <div class="chart-tips" v-if="activeMetric === 'shoeDefectRate'">
            <el-alert
              title="提示：点击柱状图可查看该工厂各线别的详细数据"
              type="info"
              :closable="false"
              show-icon
              class="chart-alert"
            />
          </div>
        </div>
      </template>
    </el-skeleton>
  </div>
</template>

<style lang="scss" scoped>
:root {
  --color-primary: #3498DB;
  --color-white: #FFFFFF;
  --color-text-primary: #303133;
  --color-text-secondary: #909399;
  --color-border-lighter: #EBEEF5;
  --color-kpi-orange: #F39C12;
  --color-kpi-purple: #8E44AD;
  --color-kpi-blue: var(--color-primary);
  --color-kpi-green: #2ECC71;
  --card-padding: 15px;
  --grid-gap: 15px;
  --card-border-radius: 4px;
}

.quality-metrics-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 20px 0;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--color-border-lighter);
  text-align: center;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  margin-bottom: 20px;
  min-height: 120px;
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: 10px;
  background-color: var(--color-white);
  border-radius: var(--card-border-radius);
  border: 1px solid var(--color-border-lighter);
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-height: 100px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    
    .metric-subtitle {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &.active {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
    
    .metric-subtitle {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &.metric-orange { 
    border-bottom-color: var(--color-kpi-orange);
    &:hover, &.active { box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3); }
    &.active { background-color: rgba(243, 156, 18, 0.05); }
  }
  &.metric-purple { 
    border-bottom-color: var(--color-kpi-purple);
    &:hover, &.active { box-shadow: 0 4px 12px rgba(142, 68, 173, 0.3); }
    &.active { background-color: rgba(142, 68, 173, 0.05); }
  }
  &.metric-blue { 
    border-bottom-color: var(--color-kpi-blue);
    &:hover, &.active { box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3); }
    &.active { background-color: rgba(52, 152, 219, 0.05); }
  }
  &.metric-green { 
    border-bottom-color: var(--color-kpi-green);
    &:hover, &.active { box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3); }
    &.active { background-color: rgba(46, 204, 113, 0.05); }
  }
}

.metric-title {
  font-size: 14px;
  color: var(--color-text-secondary);
  margin-bottom: 8px;
  font-weight: 500;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  
  .metric-item.metric-orange & { color: var(--color-kpi-orange); }
  .metric-item.metric-purple & { color: var(--color-kpi-purple); }
  .metric-item.metric-blue & { color: var(--color-kpi-blue); }
  .metric-item.metric-green & { color: var(--color-kpi-green); }
}

.metric-subtitle {
  font-size: 11px;
  color: var(--color-text-secondary);
  opacity: 0;
  transform: translateY(5px);
  transition: all 0.3s ease;
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%) translateY(5px);
  white-space: nowrap;
}

.chart-section {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.chart-container {
  flex-grow: 1;
  width: 100%;
  min-height: 280px;
}

.chart-tips {
  margin-top: 10px;
  
  .chart-alert {
    border-radius: 4px;
    
    :deep(.el-alert__content) {
      font-size: 12px;
    }
    
    :deep(.el-alert__title) {
      font-size: 12px;
      margin-bottom: 0;
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }
  
  .metric-item {
    padding: 8px;
    min-height: 90px;
  }
  
  .metric-value {
    font-size: 20px;
  }
  
  .metric-title {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .metric-value {
    font-size: 20px;
  }
  
  .card-title {
    font-size: 16px;
    margin-bottom: 15px;
  }
  
  .chart-container {
    min-height: 250px;
  }
}
</style>
