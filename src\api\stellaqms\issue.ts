import request from '@/config/axios'

export interface IssueVO {
  id: number
  issueNo: string
  brand: string
  factory: string
  issueType: string
  img?: string | string[]
  department: string
  teamName: string
  agentName: string
  deptName: string
  publisher: string
  publisherName: string
  assigneeName: string
  informedPersonName: string
  facilityManagerName: string
  factoryManagerName: string
  status: number
  issueRemark?: string
  publishDate: Date
  defectName: string
  model: string
  orderNumber: string
  finishDate?: Date
}

export interface IssuePageReqVO extends PageParam {
  id?: number
  brand?: string
  factory?: string
  issueType?: string
  status?: number
  departmentId?: number
  publishDate?: string[]
}

// 定义分支数据接口
export interface BranchDataVO {
  brands: string[]
  factories: string[]
  departments: { value: number, text: string }[]
}

// 定义状态统计接口
export interface StatusCountVO {
  pendingCount: number
  processingCount: number
  confirmingCount: number
  resolvedCount: number
}

// 定义效率数据请求参数
export interface IssueEfficiencyReqVO {
  timeUnit: string
  brand?: string
  factory?: string
  departmentId?: number
  viewType?: string
}

// 定义效率数据返回值
export interface IssueEfficiencyVO {
  factories: string[]
  departments?: string[]
  avgResolutionTimes: number[]
  resolutionRates: number[]
  issueCounts: number[]
  timeUnit: string
  viewType?: string
}

// 定义工厂问题统计数据请求参数
export interface IssueFactoryStatReqVO {
  timeUnit: string
  brand?: string
  departmentId?: number
  factory?: string
}

// 定义工厂问题统计数据返回值
export interface IssueFactoryStatVO {
  factories: string[]
  departments?: string[]
  timeUnit: string
  totalIssues: Record<string, number>
  unsolvedIssues: Record<string, number>
  processingIssues: Record<string, number>
  confirmingIssues: Record<string, number>
  solvedIssues: Record<string, number>
}

// 定义问题回复接口
export interface IssueReplyVO {
  id?: number
  parentId?: number
  content: string
  img?: string | string[]
  replyTime?: Date
  user?: string 
  userName?: string
  replyContent?: string
  replyUser?: string
  replyUserName?: string
}

// 定义问题回复创建请求参数
export interface IssueReplyCreateReqVO {
  issueId: number
  content: string
  img?: string | string[]
  replyUser?: string
  replyContent?: string
}

// 查询问题列表
export const getIssuePage = (params: IssuePageReqVO) => {
  return request.get({ url: '/stella-qms/issue/page', params })
}

// 查询问题详情
export const getIssue = (id: number) => {
  return request.get({ url: '/stella-qms/issue/get?id=' + id })
}

// 新增问题
export const createIssue = (data: IssueVO) => {
  return request.post({ url: '/stella-qms/issue/create', data })
}

// 修改问题
export const updateIssue = (data: IssueVO) => {
  return request.put({ url: '/stella-qms/issue/update', data })
}

// 删除问题
export const deleteIssue = (id: number) => {
  return request.delete({ url: '/stella-qms/issue/delete?id=' + id })
}

// 导出问题
export const exportIssue = (params: IssuePageReqVO) => {
  return request.download({ url: '/stella-qms/issue/export-excel', params })
}

// 更新问题状态
export const updateIssueStatus = (id: number, status: number, remark: string) => {
  const data = {
    id,
    status,
    remark
  }
  return request.put({ url: '/stella-qms/issue/update-status', data })
}

// 获取问题统计数据
export const getIssueStatistics = () => {
  return request.get({ url: '/stella-qms/issue/statistics' })
}

// 获取问题趋势数据
export const getIssueTrend = () => {
  return request.get({ url: '/stella-qms/issue/trend' })
}

// 获取问题分类统计
export const getIssueTypeStatistics = () => {
  return request.get({ url: '/stella-qms/issue/type-statistics' })
}

// 获取问题分支数据（品牌、工厂和部门）
export const getBranchData = () => {
  return request.get<BranchDataVO>({ url: '/stella-qms/issue/branch-data' })
}

// 获取问题状态统计
export const getIssueStatusCount = (params: IssuePageReqVO) => {
  return request.get<StatusCountVO>({ url: '/stella-qms/issue/status-count', params })
}

// 获取工厂问题解决效率数据
export const getIssueEfficiencyData = (params: IssueEfficiencyReqVO) => {
  return request.get<IssueEfficiencyVO>({ url: '/stella-qms/issue/efficiency-data', params })
}

// 获取工厂问题统计数据
export const getIssueFactoryStat = (params: IssueFactoryStatReqVO) => {
  return request.get<IssueFactoryStatVO>({ url: '/stella-qms/issue/factory-stat', params })
}

// 创建问题回复
export const createIssueReply = (data: IssueReplyCreateReqVO) => {
  return request.post({ url: '/stella-qms/issue/reply/create', data })
}

// 获取问题回复列表
export const getIssueReplyList = (issueId: number) => {
  return request.get<IssueReplyVO[]>({ url: '/stella-qms/issue/reply/list-by-issue', params: { issueId } })
} 