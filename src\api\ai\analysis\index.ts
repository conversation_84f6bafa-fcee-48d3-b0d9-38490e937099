import request from '@/config/axios'

// AI 仪表板摘要响应 VO
export interface AiDashboardSummaryRespVO {
  success: boolean // 是否成功
  data?: string // AI 分析结果文本
  message?: string // 错误或提示信息
}

// AI 仪表板摘要请求 VO (根据需要扩展)
export interface AiDashboardSummaryReqVO {
  factoryId?: number // 工厂ID (可选)
  dateRange?: string[] // 日期范围 (可选)
  // 可以根据实际需求添加更多字段
}

// AI 分析 API
export const AiAnalysisApi = {
  // 获取仪表板AI摘要
  getDashboardSummary: async (data?: AiDashboardSummaryReqVO) => {
    return await request.get({ 
      url: `/qms/ai-analysis/dashboard-summary`, 
      data 
    })
  }
} 